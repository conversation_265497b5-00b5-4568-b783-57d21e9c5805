"use client";
import Konva from "konva";
import { Stage, Layer, Transformer } from "react-konva";
import ZoomLevel from "./ZoomLevel";
import { useSeatMapContext } from "../context";
import useSeatMapStore from "@stores/useSeatMapStore";
import LayoutInfo from "./LayoutInfo";
import useDimensionsListener from "../hooks/useDimensionsListener";
import { maintainSquare } from "../lib/dimensions";
import SeatInteractionPopup from "./SeatInteractionPopup";
import { InitialLoadingScreen } from "./InitialLoadingScreen";
import SeatMapCacheManager from "../lib/cache/seatMapCache";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { SeatPosition } from "../lib/seats";

export default function PreCachedCanvas() {
  useDimensionsListener();
  const { stageRef, layerRef, dragLayerRef, transformerRef } = useSeatMapContext();
  const { 
    stageScale, 
    stageDimensions, 
    layoutDimensions, 
    setLayoutDimensions,
    seatSections,
    setStageScale,
    toggleSeat
  } = useSeatMapStore();

  // Pre-caching state
  const [isPreCaching, setIsPreCaching] = useState(false);
  const [cacheProgress, setCacheProgress] = useState(0);
  const [cacheMessage, setCacheMessage] = useState("");
  const [isCacheComplete, setIsCacheComplete] = useState(false);
  
  // Seat interaction state
  const [selectedSeat, setSelectedSeat] = useState<SeatPosition | null>(null);
  const [selectedSectionName, setSelectedSectionName] = useState("");
  const [popupPosition, setPopupPosition] = useState<{ x: number; y: number } | null>(null);
  
  // Cache manager
  const cacheManagerRef = useRef<SeatMapCacheManager | null>(null);
  const currentLayerRef = useRef<Konva.Layer | null>(null);

  // Calculate total seats
  const totalSeats = useMemo(() => {
    return Array.from(seatSections.values()).reduce((total, section) => total + section.seats.size, 0);
  }, [seatSections]);

  // Handle seat click
  const handleSeatClick = useCallback((sectionId: string, seatId: string, clickPosition?: { x: number; y: number }) => {
    const section = seatSections.get(sectionId);
    if (section) {
      const seat = section.seats.get(seatId);
      if (seat) {
        setSelectedSeat(seat);
        setSelectedSectionName(section.name);
        setPopupPosition(clickPosition || { x: 100, y: 100 });
      }
    }
  }, [seatSections]);

  // Handle seat toggle
  const handleSeatToggle = useCallback(async (isActive: boolean) => {
    if (selectedSeat && cacheManagerRef.current) {
      // Update in store
      const section = Array.from(seatSections.values()).find(s => 
        s.seats.has(selectedSeat.id)
      );
      if (section) {
        toggleSeat(section.id, selectedSeat.id);
        
        // Update in cache
        await cacheManagerRef.current.updateSeatState(section.id, selectedSeat.id, isActive);
        
        // Force re-render of current layer
        if (currentLayerRef.current) {
          currentLayerRef.current.batchDraw();
        }
      }
    }
  }, [selectedSeat, seatSections, toggleSeat]);

  // Initialize cache manager
  useEffect(() => {
    if (stageRef.current && !cacheManagerRef.current) {
      cacheManagerRef.current = new SeatMapCacheManager(
        stageRef,
        (sectionId, seatId) => {
          // Get click position from stage
          const stage = stageRef.current;
          if (stage) {
            const pointerPos = stage.getPointerPosition();
            handleSeatClick(sectionId, seatId, pointerPos || undefined);
          } else {
            handleSeatClick(sectionId, seatId);
          }
        }
      );
    }
  }, [stageRef, handleSeatClick]);

  // Pre-cache everything when sections change
  useEffect(() => {
    const preCacheSeats = async () => {
      if (
        cacheManagerRef.current && 
        seatSections.size > 0 && 
        !isCacheComplete &&
        !isPreCaching
      ) {
        setIsPreCaching(true);
        setCacheProgress(0);
        setCacheMessage("Starting pre-cache...");

        try {
          await cacheManagerRef.current.preCacheEverything(
            seatSections,
            (progress, message) => {
              setCacheProgress(progress);
              setCacheMessage(message);
            }
          );
          
          setIsCacheComplete(true);
          setCacheMessage("Cache complete! Ready for interaction.");
          
          // Set initial zoom level
          const bestZoomLevel = cacheManagerRef.current.getBestZoomLevel(stageScale);
          if (bestZoomLevel && layerRef.current) {
            // Replace layer content with cached layer
            layerRef.current.removeChildren();
            layerRef.current.add(...bestZoomLevel.renderedLayer.getChildren());
            currentLayerRef.current = layerRef.current;
            layerRef.current.batchDraw();
          }
          
        } catch (error) {
          console.error("Pre-caching failed:", error);
          setCacheMessage("Cache failed. Using fallback rendering.");
        } finally {
          setTimeout(() => {
            setIsPreCaching(false);
          }, 1000);
        }
      }
    };

    preCacheSeats();
  }, [seatSections, isCacheComplete, isPreCaching, stageScale, layerRef]);

  // Handle zoom changes
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();

    if (!stageRef.current || !cacheManagerRef.current || !isCacheComplete) return;
    
    const stage = stageRef.current;
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    let direction = e.evt.deltaY > 0 ? 1 : -1;
    if (e.evt.ctrlKey) {
      direction = -direction;
    }

    const scaleBy = 1.05;
    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    if (newScale < 0.05 || newScale >= 8.01) return;

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };

    stage.position(newPos);
    setStageScale(newScale);

    // Get best cached zoom level for new scale
    const bestZoomLevel = cacheManagerRef.current.getBestZoomLevel(newScale);
    if (bestZoomLevel && layerRef.current) {
      // Instantly switch to cached layer
      layerRef.current.removeChildren();
      layerRef.current.add(...bestZoomLevel.renderedLayer.getChildren());
      currentLayerRef.current = layerRef.current;
      layerRef.current.batchDraw();
    }
  }, [stageRef, setStageScale, isCacheComplete, layerRef]);

  const handleStageClick = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
    const target = e.target;
    if (target === e.target.getStage()) {
      // Clicked on empty stage
      setSelectedSeat(null);
      setPopupPosition(null);
    }
  }, []);

  const handleLayoutClick = useCallback(() => {
    setLayoutDimensions({ ...layoutDimensions, selected: !layoutDimensions.selected });
  }, [layoutDimensions, setLayoutDimensions]);

  return (
    <div className="bg-gray-light100 relative flex h-[calc(100vh-72px)] w-full items-center justify-center">
      <ZoomLevel stageScale={stageScale} />
      <LayoutInfo />
      
      {/* Pre-caching Loading Screen */}
      <InitialLoadingScreen
        isVisible={isPreCaching}
        totalSeats={totalSeats}
        loadedSeats={Math.round((cacheProgress / 100) * totalSeats)}
      />

      {/* Seat Interaction Popup */}
      <SeatInteractionPopup
        seat={selectedSeat}
        sectionName={selectedSectionName}
        position={popupPosition}
        onToggle={handleSeatToggle}
        onClose={() => {
          setSelectedSeat(null);
          setPopupPosition(null);
        }}
      />

      <Stage
        ref={stageRef}
        className="border-l border-gray-200 transition-opacity duration-150"
        width={stageDimensions.w}
        height={stageDimensions.h}
        onWheel={handleWheel}
        onClick={handleStageClick}
        draggable
      >
        <Layer ref={layerRef} x={(stageDimensions.w - 500) / 2} y={(stageDimensions.h - 500) / 2}>
          {/* Pre-cached content will be added here */}
          <Transformer keepRatio={true} boundBoxFunc={maintainSquare} ref={transformerRef} />
        </Layer>
        <Layer ref={dragLayerRef} x={(stageDimensions.w - 500) / 2} y={(stageDimensions.h - 500) / 2} />
      </Stage>

      {/* Cache Status (for debugging) */}
      {isCacheComplete && (
        <div className="absolute bottom-4 right-4 bg-green-100 border border-green-300 rounded-lg px-3 py-2 text-sm">
          <div className="text-green-800 font-medium">✅ Cache Ready</div>
          <div className="text-green-600 text-xs">
            {totalSeats.toLocaleString()} seats • Instant zoom
          </div>
        </div>
      )}
    </div>
  );
}
