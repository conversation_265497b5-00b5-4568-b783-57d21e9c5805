"use client";
import React from "react";
import useSeatMapStore from "@stores/useSeatMapStore";

interface InitialLoadingScreenProps {
  isVisible: boolean;
  totalSeats: number;
  loadedSeats: number;
}

export const InitialLoadingScreen: React.FC<InitialLoadingScreenProps> = ({
  isVisible,
  totalSeats,
  loadedSeats
}) => {
  const { optimizationProgress } = useSeatMapStore();
  
  if (!isVisible) return null;

  const progress = totalSeats > 0 ? (loadedSeats / totalSeats) * 100 : optimizationProgress;

  return (
    <div className="absolute inset-0 z-50 flex items-center justify-center bg-white/95 backdrop-blur-sm">
      <div className="flex flex-col items-center gap-6 rounded-xl bg-white p-8 shadow-2xl border border-gray-200">
        {/* Main Loading Animation */}
        <div className="relative">
          <div className="h-16 w-16 animate-spin rounded-full border-4 border-gray-200 border-t-blue-600" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-sm font-bold text-blue-600">
              {Math.round(progress)}%
            </div>
          </div>
        </div>

        {/* Title */}
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">
            Creating Large Seat Map
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            Optimizing {totalSeats.toLocaleString()} seats for best performance
          </p>
        </div>

        {/* Progress Bar */}
        <div className="w-80">
          <div className="flex justify-between text-xs text-gray-500 mb-2">
            <span>Loading seats...</span>
            <span>{loadedSeats.toLocaleString()} / {totalSeats.toLocaleString()}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Tips */}
        <div className="text-center max-w-md">
          <p className="text-xs text-gray-500">
            💡 <strong>Tip:</strong> Large seat maps are automatically optimized. 
            Use zoom controls to navigate efficiently.
          </p>
        </div>

        {/* Performance Info */}
        <div className="flex items-center gap-4 text-xs text-gray-400">
          <div className="flex items-center gap-1">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            <span>Progressive Loading</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
            <span>Viewport Culling</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="h-2 w-2 bg-purple-500 rounded-full animate-pulse" />
            <span>Smart Caching</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Compact version for smaller operations
export const CompactInitialLoader: React.FC<{ isVisible: boolean; message?: string }> = ({ 
  isVisible, 
  message = "Preparing seat map..." 
}) => {
  if (!isVisible) return null;

  return (
    <div className="absolute inset-0 z-40 flex items-center justify-center bg-black/20 backdrop-blur-[1px]">
      <div className="flex items-center gap-3 rounded-lg bg-white/95 px-6 py-4 shadow-lg backdrop-blur-sm border border-gray-200">
        <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
        <span className="text-sm font-medium text-gray-700">{message}</span>
      </div>
    </div>
  );
};

export default InitialLoadingScreen;
