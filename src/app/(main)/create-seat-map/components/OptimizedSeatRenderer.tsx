"use client";
import React, { useCallback, useMemo, useRef, useEffect, useState } from "react";
import { Group, Rect, Text } from "react-konva";
import Konva from "konva";
import { SeatPosition, SeatSection } from "../lib/seats";
import { SEAT_SIZE, ZOOM_PERFORMANCE_CONFIG } from "../lib/config";
import useSeatMapStore from "@stores/useSeatMapStore";
import { getAdaptiveQuality } from "../lib/performance/optimization";

interface OptimizedSeatRendererProps {
  section: SeatSection;
  stageScale: number;
  viewportBounds: { x: number; y: number; width: number; height: number };
}

interface SeatItemProps {
  seat: SeatPosition;
  sectionId: string;
  stageScale: number;
  onSeatClick: (sectionId: string, seatId: string) => void;
}

// Highly optimized seat component with scale-based rendering
const OptimizedSeatItem = React.memo(({ seat, sectionId, stageScale, onSeatClick }: SeatItemProps) => {
  const handleClick = useCallback(() => {
    onSeatClick(sectionId, seat.id);
  }, [sectionId, seat.id, onSeatClick]);

  const seatColor = seat.isActive ? "#3b82f6" : "#9ca3af";
  const textColor = seat.isActive ? "white" : "#374151";

  // Adaptive rendering based on zoom level using config
  const shouldShowText = stageScale > ZOOM_PERFORMANCE_CONFIG.TEXT_VISIBILITY_THRESHOLD;
  const shouldShowDetails = stageScale > ZOOM_PERFORMANCE_CONFIG.AGGRESSIVE_OPTIMIZATION_THRESHOLD;
  
  if (!shouldShowDetails) {
    // Ultra-simplified rendering for very zoomed out views
    return (
      <Rect
        x={seat.x}
        y={seat.y}
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        onClick={handleClick}
        onTap={handleClick}
        listening={true}
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
      />
    );
  }

  return (
    <Group
      x={seat.x}
      y={seat.y}
      onClick={handleClick}
      onTap={handleClick}
      listening={true}
    >
      <Rect
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        stroke={seat.isActive ? "#1d4ed8" : "#6b7280"}
        strokeWidth={stageScale > 1 ? 1 : 0} // Remove stroke when zoomed out
        cornerRadius={stageScale > 0.8 ? 2 : 0} // Remove corner radius when zoomed out
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
      />
      {shouldShowText && (
        <Text
          width={SEAT_SIZE}
          height={SEAT_SIZE}
          text={seat.label}
          fontSize={Math.min(SEAT_SIZE / 3.5, 8) * Math.min(stageScale, 1.5)}
          fontFamily="Arial"
          fill={textColor}
          align="center"
          verticalAlign="middle"
          perfectDrawEnabled={false}
          listening={false}
        />
      )}
    </Group>
  );
});

OptimizedSeatItem.displayName = "OptimizedSeatItem";

// Enhanced viewport culling with buffer zones
const isInViewportOptimized = (
  seatX: number,
  seatY: number,
  sectionX: number,
  sectionY: number,
  viewportBounds: { x: number; y: number; width: number; height: number },
  stageScale: number
): boolean => {
  // Adaptive buffer based on zoom level - larger buffer when zoomed out
  const buffer = Math.max(100, 500 / stageScale);
  
  const absoluteX = sectionX + seatX;
  const absoluteY = sectionY + seatY;
  
  return (
    absoluteX + SEAT_SIZE >= viewportBounds.x - buffer &&
    absoluteX <= viewportBounds.x + viewportBounds.width + buffer &&
    absoluteY + SEAT_SIZE >= viewportBounds.y - buffer &&
    absoluteY <= viewportBounds.y + viewportBounds.height + buffer
  );
};

export const OptimizedSeatRenderer: React.FC<OptimizedSeatRendererProps> = ({ 
  section, 
  stageScale, 
  viewportBounds 
}) => {
  const { toggleSeat, enableVirtualization } = useSeatMapStore();
  const groupRef = useRef<Konva.Group>(null);
  const [isZooming, setIsZooming] = useState(false);
  const zoomTimeoutRef = useRef<NodeJS.Timeout>();

  const handleSeatClick = useCallback((sectionId: string, seatId: string) => {
    toggleSeat(sectionId, seatId);
  }, [toggleSeat]);

  // Detect zoom state changes
  useEffect(() => {
    setIsZooming(true);
    
    if (zoomTimeoutRef.current) {
      clearTimeout(zoomTimeoutRef.current);
    }
    
    zoomTimeoutRef.current = setTimeout(() => {
      setIsZooming(false);
    }, 150); // Consider zoom finished after 150ms of no scale changes

    return () => {
      if (zoomTimeoutRef.current) {
        clearTimeout(zoomTimeoutRef.current);
      }
    };
  }, [stageScale]);

  // Aggressive viewport culling with scale-based optimization
  const visibleSeats = useMemo(() => {
    if (!enableVirtualization) {
      return Array.from(section.seats.values());
    }

    // When zooming, reduce the number of rendered seats for better performance
    const seats = Array.from(section.seats.values());
    
    if (isZooming && stageScale < ZOOM_PERFORMANCE_CONFIG.TEXT_VISIBILITY_THRESHOLD) {
      // When zoomed out and zooming, show only every nth seat for performance
      const skipFactor = Math.min(ZOOM_PERFORMANCE_CONFIG.ZOOM_OUT_SKIP_FACTOR, Math.floor(1 / stageScale));
      const filteredSeats = seats.filter((seat, index) =>
        index % Math.max(1, skipFactor) === 0 &&
        isInViewportOptimized(seat.x, seat.y, section.x, section.y, viewportBounds, stageScale)
      );

      // Limit total seats during zoom for performance
      return filteredSeats.slice(0, ZOOM_PERFORMANCE_CONFIG.MAX_SEATS_DURING_ZOOM);
    }

    return seats.filter(seat =>
      isInViewportOptimized(seat.x, seat.y, section.x, section.y, viewportBounds, stageScale)
    );
  }, [section.seats, section.x, section.y, viewportBounds, enableVirtualization, stageScale, isZooming]);

  // Enhanced caching strategy
  useEffect(() => {
    const group = groupRef.current;
    if (!group) return;

    // Only cache when not zooming and when we have many seats
    if (!isZooming && visibleSeats.length > 100) {
      const quality = getAdaptiveQuality(stageScale);
      
      // Use lower quality cache during zoom operations
      group.cache({
        pixelRatio: isZooming ? Math.min(quality.pixelRatio, 1) : quality.pixelRatio,
        imageSmoothingEnabled: !isZooming && quality.imageSmoothingEnabled
      });
      
      return () => {
        group.clearCache();
      };
    } else if (isZooming) {
      // Clear cache during zoom for better performance
      group.clearCache();
    }
  }, [visibleSeats.length, stageScale, isZooming]);

  // Skip rendering section background when zoomed out significantly
  const shouldShowSectionDetails = stageScale > ZOOM_PERFORMANCE_CONFIG.SECTION_DETAILS_THRESHOLD;

  return (
    <Group
      ref={groupRef}
      x={section.x}
      y={section.y}
      listening={true}
    >
      {shouldShowSectionDetails && (
        <>
          {/* Section background */}
          <Rect
            x={0}
            y={0}
            width={section.width}
            height={section.height}
            fill="rgba(200, 200, 200, 0.1)"
            stroke="#d1d5db"
            strokeWidth={stageScale > 0.5 ? 1 : 0}
            listening={false}
            perfectDrawEnabled={false}
          />
          
          {/* Section label - only show when zoomed in enough */}
          {stageScale > 0.4 && (
            <Text
              x={10}
              y={10}
              text={section.name}
              fontSize={Math.min(14 * stageScale, 14)}
              fontFamily="Arial"
              fill="#374151"
              listening={false}
              perfectDrawEnabled={false}
            />
          )}
        </>
      )}

      {/* Render visible seats */}
      {visibleSeats.map((seat) => (
        <OptimizedSeatItem
          key={seat.id}
          seat={seat}
          sectionId={section.id}
          stageScale={stageScale}
          onSeatClick={handleSeatClick}
        />
      ))}
    </Group>
  );
};

export default OptimizedSeatRenderer;
