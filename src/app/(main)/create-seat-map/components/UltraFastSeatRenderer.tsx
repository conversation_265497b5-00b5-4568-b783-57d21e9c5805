"use client";
import React, { useCallback, useMemo, useRef, useEffect, useState } from "react";
import { Group, Rect, Text } from "react-konva";
import Konva from "konva";
import { SeatPosition, SeatSection } from "../lib/seats";
import { SEAT_SIZE, ZOOM_PERFORMANCE_CONFIG, INITIAL_RENDER_CONFIG } from "../lib/config";
import useSeatMapStore from "@stores/useSeatMapStore";

interface UltraFastSeatRendererProps {
  section: SeatSection;
  stageScale: number;
  viewportBounds: { x: number; y: number; width: number; height: number };
}

interface FastSeatItemProps {
  seat: SeatPosition;
  sectionId: string;
  stageScale: number;
  isZooming: boolean;
  onSeatClick: (sectionId: string, seatId: string) => void;
}

// Ultra-optimized seat component with zoom-aware rendering
const FastSeatItem = React.memo(({ seat, sectionId, stageScale, isZooming, onSeatClick }: FastSeatItemProps) => {
  const handleClick = useCallback(() => {
    onSeatClick(sectionId, seat.id);
  }, [sectionId, seat.id, onSeatClick]);

  const seatColor = seat.isActive ? "#3b82f6" : "#9ca3af";

  // During zoom, use ultra-simplified rendering
  if (isZooming) {
    return (
      <Rect
        x={seat.x}
        y={seat.y}
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        onClick={handleClick}
        onTap={handleClick}
        listening={true}
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
        strokeEnabled={false} // No stroke during zoom
      />
    );
  }

  // Static rendering with full details
  const shouldShowText = stageScale > ZOOM_PERFORMANCE_CONFIG.TEXT_VISIBILITY_THRESHOLD;
  const shouldShowDetails = stageScale > ZOOM_PERFORMANCE_CONFIG.AGGRESSIVE_OPTIMIZATION_THRESHOLD;
  
  if (!shouldShowDetails) {
    return (
      <Rect
        x={seat.x}
        y={seat.y}
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        onClick={handleClick}
        onTap={handleClick}
        listening={true}
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
      />
    );
  }

  return (
    <Group
      x={seat.x}
      y={seat.y}
      onClick={handleClick}
      onTap={handleClick}
      listening={true}
    >
      <Rect
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        stroke={seat.isActive ? "#1d4ed8" : "#6b7280"}
        strokeWidth={stageScale > 1 ? 1 : 0}
        cornerRadius={stageScale > 0.8 ? 2 : 0}
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
      />
      {shouldShowText && (
        <Text
          width={SEAT_SIZE}
          height={SEAT_SIZE}
          text={seat.label}
          fontSize={Math.min(SEAT_SIZE / 3.5, 8) * Math.min(stageScale, 1.5)}
          fontFamily="Arial"
          fill="white"
          align="center"
          verticalAlign="middle"
          perfectDrawEnabled={false}
          listening={false}
        />
      )}
    </Group>
  );
});

FastSeatItem.displayName = "FastSeatItem";

// Ultra-aggressive viewport culling
const isInViewportUltraFast = (
  seatX: number,
  seatY: number,
  sectionX: number,
  sectionY: number,
  viewportBounds: { x: number; y: number; width: number; height: number },
  stageScale: number,
  isZooming: boolean
): boolean => {
  // If viewport bounds are not initialized, show all seats
  if (viewportBounds.width === 0 || viewportBounds.height === 0) {
    return true;
  }

  // During zoom, use much smaller buffer for aggressive culling
  const buffer = isZooming ? Math.max(50, 200 / stageScale) : Math.max(100, 500 / stageScale);

  const absoluteX = sectionX + seatX;
  const absoluteY = sectionY + seatY;

  return (
    absoluteX + SEAT_SIZE >= viewportBounds.x - buffer &&
    absoluteX <= viewportBounds.x + viewportBounds.width + buffer &&
    absoluteY + SEAT_SIZE >= viewportBounds.y - buffer &&
    absoluteY <= viewportBounds.y + viewportBounds.height + buffer
  );
};

export const UltraFastSeatRenderer: React.FC<UltraFastSeatRendererProps> = ({ 
  section, 
  stageScale, 
  viewportBounds 
}) => {
  const { toggleSeat, enableVirtualization, isZooming } = useSeatMapStore();
  const groupRef = useRef<Konva.Group>(null);
  const [cachedSeats, setCachedSeats] = useState<SeatPosition[]>([]);
  const lastNonZoomSeats = useRef<SeatPosition[]>([]);

  const handleSeatClick = useCallback((sectionId: string, seatId: string) => {
    toggleSeat(sectionId, seatId);
  }, [toggleSeat]);

  const totalSeats = section.seats.size;
  const isUltraFastMode = totalSeats > ZOOM_PERFORMANCE_CONFIG.ULTRA_FAST_ZOOM_THRESHOLD;

  // FREEZE APPROACH: Don't update seats during zoom at all
  const visibleSeats = useMemo(() => {
    // If zooming, return the last cached seats - NO RECALCULATION
    if (isZooming && cachedSeats.length > 0) {
      return cachedSeats;
    }

    // Only calculate when NOT zooming
    const allSeats = Array.from(section.seats.values());

    if (!enableVirtualization) {
      return allSeats.slice(0, 5000); // Limit for performance
    }

    // Calculate viewport seats only when not zooming
    const viewportSeats = allSeats.filter(seat =>
      isInViewportUltraFast(seat.x, seat.y, section.x, section.y, viewportBounds, stageScale, false)
    );

    const result = viewportSeats.length > 0 ? viewportSeats : allSeats.slice(0, 2000);

    // Cache the result for zoom operations
    setCachedSeats(result);

    return result;
  }, [section.seats, section.x, section.y, viewportBounds, enableVirtualization, stageScale, isZooming, cachedSeats]);

  // Clear cache during zoom for maximum performance
  useEffect(() => {
    const group = groupRef.current;
    if (!group) return;

    if (isZooming) {
      // Clear all caches during zoom
      group.clearCache();
    } else if (visibleSeats.length > 50) {
      // Only cache when not zooming
      setTimeout(() => {
        if (!isZooming && group) {
          group.cache({
            pixelRatio: Math.min(stageScale < 0.5 ? 1 : 2, window.devicePixelRatio),
            imageSmoothingEnabled: false // Disable for performance
          });
        }
      }, 100); // Delay to ensure zoom has stopped
    }
  }, [isZooming, visibleSeats.length, stageScale]);

  const shouldShowSectionDetails = stageScale > ZOOM_PERFORMANCE_CONFIG.SECTION_DETAILS_THRESHOLD && !isZooming;

  return (
    <Group
      ref={groupRef}
      x={section.x}
      y={section.y}
      listening={!isZooming} // Disable interaction during zoom for performance
    >
      {shouldShowSectionDetails && (
        <>
          <Rect
            x={0}
            y={0}
            width={section.width}
            height={section.height}
            fill="rgba(200, 200, 200, 0.1)"
            stroke="#d1d5db"
            strokeWidth={stageScale > 0.5 ? 1 : 0}
            listening={false}
            perfectDrawEnabled={false}
          />
          
          {stageScale > 0.4 && (
            <Text
              x={10}
              y={10}
              text={`${section.name} (${visibleSeats.length}/${totalSeats})`}
              fontSize={Math.min(14 * stageScale, 14)}
              fontFamily="Arial"
              fill="#374151"
              listening={false}
              perfectDrawEnabled={false}
            />
          )}
        </>
      )}

      {/* Render visible seats */}
      {visibleSeats.map((seat) => (
        <FastSeatItem
          key={seat.id}
          seat={seat}
          sectionId={section.id}
          stageScale={stageScale}
          isZooming={isZooming}
          onSeatClick={handleSeatClick}
        />
      ))}
    </Group>
  );
};

export default UltraFastSeatRenderer;
