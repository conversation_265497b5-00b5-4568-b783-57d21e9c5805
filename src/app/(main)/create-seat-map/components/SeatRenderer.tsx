"use client";
import React, { useCallback, useMemo, useRef, useEffect } from "react";
import { Group, Rect, Text } from "react-konva";
import Konva from "konva";
import { SeatPosition, SeatSection } from "../lib/seats";
import { SEAT_SIZE } from "../lib/config";
import useSeatMapStore from "@stores/useSeatMapStore";
import { getAdaptiveQuality } from "../lib/performance/optimization";

interface SeatRendererProps {
  section: SeatSection;
  stageScale: number;
  viewportBounds: { x: number; y: number; width: number; height: number };
}

interface SeatItemProps {
  seat: SeatPosition;
  sectionId: string;
  onSeatClick: (sectionId: string, seatId: string) => void;
}

// Memoized individual seat component for performance
const SeatItem = React.memo(({ seat, sectionId, onSeatClick }: SeatItemProps) => {
  const handleClick = useCallback(() => {
    onSeatClick(sectionId, seat.id);
  }, [sectionId, seat.id, onSeatClick]);

  const seatColor = seat.isActive ? "#3b82f6" : "#9ca3af"; // Blue for active, gray for inactive
  const textColor = seat.isActive ? "white" : "#374151";

  return (
    <Group
      x={seat.x}
      y={seat.y}
      onClick={handleClick}
      onTap={handleClick}
      listening={true}
    >
      <Rect
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        stroke={seat.isActive ? "#1d4ed8" : "#6b7280"}
        strokeWidth={1}
        cornerRadius={2}
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
      />
      <Text
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        text={seat.label}
        fontSize={Math.min(SEAT_SIZE / 3.5, 8)}
        fontFamily="Arial"
        fill={textColor}
        align="center"
        verticalAlign="middle"
        perfectDrawEnabled={false}
        listening={false}
      />
    </Group>
  );
});

SeatItem.displayName = "SeatItem";

// Viewport culling utility
const isInViewport = (
  seatX: number,
  seatY: number,
  sectionX: number,
  sectionY: number,
  viewportBounds: { x: number; y: number; width: number; height: number },
  buffer: number = 100
): boolean => {
  const absoluteX = sectionX + seatX;
  const absoluteY = sectionY + seatY;
  
  return (
    absoluteX + SEAT_SIZE >= viewportBounds.x - buffer &&
    absoluteX <= viewportBounds.x + viewportBounds.width + buffer &&
    absoluteY + SEAT_SIZE >= viewportBounds.y - buffer &&
    absoluteY <= viewportBounds.y + viewportBounds.height + buffer
  );
};

export const SeatRenderer: React.FC<SeatRendererProps> = ({ 
  section, 
  stageScale, 
  viewportBounds 
}) => {
  const { toggleSeat, enableVirtualization } = useSeatMapStore();
  const groupRef = useRef<Konva.Group>(null);

  const handleSeatClick = useCallback((sectionId: string, seatId: string) => {
    toggleSeat(sectionId, seatId);
  }, [toggleSeat]);

  // Memoize visible seats for performance
  const visibleSeats = useMemo(() => {
    if (!enableVirtualization) {
      return Array.from(section.seats.values());
    }

    return Array.from(section.seats.values()).filter(seat =>
      isInViewport(seat.x, seat.y, section.x, section.y, viewportBounds)
    );
  }, [section.seats, section.x, section.y, viewportBounds, enableVirtualization]);

  // Performance optimization: Cache the group when not moving
  useEffect(() => {
    const group = groupRef.current;
    if (group && visibleSeats.length > 50) {
      const quality = getAdaptiveQuality(stageScale);
      // Cache large sections for better performance
      group.cache({
        pixelRatio: quality.pixelRatio,
        imageSmoothingEnabled: quality.imageSmoothingEnabled
      });

      return () => {
        group.clearCache();
      };
    }
  }, [visibleSeats.length, stageScale]);

  return (
    <Group
      ref={groupRef}
      x={section.x}
      y={section.y}
      listening={true}
    >
      {/* Section background */}
      <Rect
        x={0}
        y={0}
        width={section.width}
        height={section.height}
        fill="rgba(200, 200, 200, 0.1)"
        stroke="#d1d5db"
        strokeWidth={1}
        listening={false}
        perfectDrawEnabled={false}
      />
      
      {/* Section label */}
      <Text
        x={10}
        y={10}
        text={section.name}
        fontSize={14}
        fontFamily="Arial"
        fill="#374151"
        listening={false}
        perfectDrawEnabled={false}
      />

      {/* Render visible seats */}
      {visibleSeats.map((seat) => (
        <SeatItem
          key={seat.id}
          seat={seat}
          sectionId={section.id}
          onSeatClick={handleSeatClick}
        />
      ))}
    </Group>
  );
};

export default SeatRenderer;
