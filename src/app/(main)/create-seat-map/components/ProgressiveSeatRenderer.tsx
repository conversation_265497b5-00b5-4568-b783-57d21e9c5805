"use client";
import React, { useCallback, useMemo, useRef, useEffect, useState } from "react";
import { Group, Rect, Text } from "react-konva";
import Konva from "konva";
import { SeatPosition, SeatSection } from "../lib/seats";
import { SEAT_SIZE, INITIAL_RENDER_CONFIG, ZOOM_PERFORMANCE_CONFIG } from "../lib/config";
import useSeatMapStore from "@stores/useSeatMapStore";
import { getAdaptiveQuality } from "../lib/performance/optimization";

interface ProgressiveSeatRendererProps {
  section: SeatSection;
  stageScale: number;
  viewportBounds: { x: number; y: number; width: number; height: number };
}

interface SeatItemProps {
  seat: SeatPosition;
  sectionId: string;
  stageScale: number;
  onSeatClick: (sectionId: string, seatId: string) => void;
}

// Optimized seat component
const OptimizedSeatItem = React.memo(({ seat, sectionId, stageScale, onSeatClick }: SeatItemProps) => {
  const handleClick = useCallback(() => {
    onSeatClick(sectionId, seat.id);
  }, [sectionId, seat.id, onSeatClick]);

  const seatColor = seat.isActive ? "#3b82f6" : "#9ca3af";
  const textColor = seat.isActive ? "white" : "#374151";

  // Adaptive rendering based on zoom level
  const shouldShowText = stageScale > ZOOM_PERFORMANCE_CONFIG.TEXT_VISIBILITY_THRESHOLD;
  const shouldShowDetails = stageScale > ZOOM_PERFORMANCE_CONFIG.AGGRESSIVE_OPTIMIZATION_THRESHOLD;
  
  if (!shouldShowDetails) {
    // Ultra-simplified rendering for very zoomed out views
    return (
      <Rect
        x={seat.x}
        y={seat.y}
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        onClick={handleClick}
        onTap={handleClick}
        listening={true}
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
      />
    );
  }

  return (
    <Group
      x={seat.x}
      y={seat.y}
      onClick={handleClick}
      onTap={handleClick}
      listening={true}
    >
      <Rect
        width={SEAT_SIZE}
        height={SEAT_SIZE}
        fill={seatColor}
        stroke={seat.isActive ? "#1d4ed8" : "#6b7280"}
        strokeWidth={stageScale > 1 ? 1 : 0}
        cornerRadius={stageScale > 0.8 ? 2 : 0}
        perfectDrawEnabled={false}
        shadowForStrokeEnabled={false}
      />
      {shouldShowText && (
        <Text
          width={SEAT_SIZE}
          height={SEAT_SIZE}
          text={seat.label}
          fontSize={Math.min(SEAT_SIZE / 3.5, 8) * Math.min(stageScale, 1.5)}
          fontFamily="Arial"
          fill={textColor}
          align="center"
          verticalAlign="middle"
          perfectDrawEnabled={false}
          listening={false}
        />
      )}
    </Group>
  );
});

OptimizedSeatItem.displayName = "OptimizedSeatItem";

// Enhanced viewport culling
const isInViewportOptimized = (
  seatX: number,
  seatY: number,
  sectionX: number,
  sectionY: number,
  viewportBounds: { x: number; y: number; width: number; height: number },
  stageScale: number
): boolean => {
  const buffer = Math.max(100, 500 / stageScale);
  const absoluteX = sectionX + seatX;
  const absoluteY = sectionY + seatY;
  
  return (
    absoluteX + SEAT_SIZE >= viewportBounds.x - buffer &&
    absoluteX <= viewportBounds.x + viewportBounds.width + buffer &&
    absoluteY + SEAT_SIZE >= viewportBounds.y - buffer &&
    absoluteY <= viewportBounds.y + viewportBounds.height + buffer
  );
};

export const ProgressiveSeatRenderer: React.FC<ProgressiveSeatRendererProps> = ({ 
  section, 
  stageScale, 
  viewportBounds 
}) => {
  const { toggleSeat, enableVirtualization, setOptimizationProgress, setIsOptimizing } = useSeatMapStore();
  const groupRef = useRef<Konva.Group>(null);
  const [loadedSeats, setLoadedSeats] = useState<Set<string>>(new Set());
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const progressiveLoadRef = useRef<NodeJS.Timeout>();

  const handleSeatClick = useCallback((sectionId: string, seatId: string) => {
    toggleSeat(sectionId, seatId);
  }, [toggleSeat]);

  const totalSeats = section.seats.size;
  const shouldUseProgressiveLoading = totalSeats > INITIAL_RENDER_CONFIG.PROGRESSIVE_LOADING_THRESHOLD;

  // Progressive loading for large seat maps
  useEffect(() => {
    if (!shouldUseProgressiveLoading) {
      // For smaller maps, load all seats immediately
      setLoadedSeats(new Set(Array.from(section.seats.keys())));
      setIsInitialLoad(false);
      return;
    }

    // For large maps, use progressive loading
    setIsOptimizing(true);
    const allSeatIds = Array.from(section.seats.keys());
    let currentIndex = 0;

    const loadNextBatch = () => {
      const batchEnd = Math.min(currentIndex + INITIAL_RENDER_CONFIG.PROGRESSIVE_BATCH_SIZE, allSeatIds.length);
      const batchIds = allSeatIds.slice(currentIndex, batchEnd);
      
      setLoadedSeats(prev => {
        const newSet = new Set(prev);
        batchIds.forEach(id => newSet.add(id));
        return newSet;
      });

      currentIndex = batchEnd;
      const progress = (currentIndex / allSeatIds.length) * 100;
      setOptimizationProgress(progress);

      if (currentIndex < allSeatIds.length) {
        progressiveLoadRef.current = setTimeout(loadNextBatch, INITIAL_RENDER_CONFIG.PROGRESSIVE_BATCH_DELAY);
      } else {
        setIsInitialLoad(false);
        setIsOptimizing(false);
        setOptimizationProgress(100);
      }
    };

    // Start with initial batch for immediate feedback
    const initialBatch = Math.min(INITIAL_RENDER_CONFIG.MAX_INITIAL_SEATS, allSeatIds.length);
    setLoadedSeats(new Set(allSeatIds.slice(0, initialBatch)));
    currentIndex = initialBatch;
    
    if (currentIndex < allSeatIds.length) {
      progressiveLoadRef.current = setTimeout(loadNextBatch, INITIAL_RENDER_CONFIG.PROGRESSIVE_BATCH_DELAY);
    } else {
      setIsInitialLoad(false);
      setIsOptimizing(false);
    }

    return () => {
      if (progressiveLoadRef.current) {
        clearTimeout(progressiveLoadRef.current);
      }
    };
  }, [section.seats, shouldUseProgressiveLoading, setIsOptimizing, setOptimizationProgress]);

  // Get visible seats with progressive loading consideration
  const visibleSeats = useMemo(() => {
    const allSeats = Array.from(section.seats.values());
    
    if (!enableVirtualization && !shouldUseProgressiveLoading) {
      return allSeats;
    }

    // Filter by loaded seats first
    let availableSeats = allSeats;
    if (shouldUseProgressiveLoading) {
      availableSeats = allSeats.filter(seat => loadedSeats.has(seat.id));
    }

    // Then apply viewport culling
    if (enableVirtualization) {
      return availableSeats.filter(seat =>
        isInViewportOptimized(seat.x, seat.y, section.x, section.y, viewportBounds, stageScale)
      );
    }

    return availableSeats;
  }, [section.seats, section.x, section.y, viewportBounds, enableVirtualization, stageScale, loadedSeats, shouldUseProgressiveLoading]);

  // Enhanced caching strategy
  useEffect(() => {
    const group = groupRef.current;
    if (!group || isInitialLoad) return;

    if (visibleSeats.length > 100) {
      const quality = getAdaptiveQuality(stageScale);
      group.cache({
        pixelRatio: quality.pixelRatio,
        imageSmoothingEnabled: quality.imageSmoothingEnabled
      });
      
      return () => {
        group.clearCache();
      };
    }
  }, [visibleSeats.length, stageScale, isInitialLoad]);

  const shouldShowSectionDetails = stageScale > ZOOM_PERFORMANCE_CONFIG.SECTION_DETAILS_THRESHOLD;

  return (
    <Group
      ref={groupRef}
      x={section.x}
      y={section.y}
      listening={true}
    >
      {shouldShowSectionDetails && (
        <>
          <Rect
            x={0}
            y={0}
            width={section.width}
            height={section.height}
            fill="rgba(200, 200, 200, 0.1)"
            stroke="#d1d5db"
            strokeWidth={stageScale > 0.5 ? 1 : 0}
            listening={false}
            perfectDrawEnabled={false}
          />
          
          {stageScale > 0.4 && (
            <Text
              x={10}
              y={10}
              text={`${section.name} (${loadedSeats.size}/${totalSeats})`}
              fontSize={Math.min(14 * stageScale, 14)}
              fontFamily="Arial"
              fill="#374151"
              listening={false}
              perfectDrawEnabled={false}
            />
          )}
        </>
      )}

      {/* Render visible seats */}
      {visibleSeats.map((seat) => (
        <OptimizedSeatItem
          key={seat.id}
          seat={seat}
          sectionId={section.id}
          stageScale={stageScale}
          onSeatClick={handleSeatClick}
        />
      ))}
    </Group>
  );
};

export default ProgressiveSeatRenderer;
