"use client";
import Kon<PERSON> from "konva";
import { Stage, Layer, Transformer } from "react-konva";
import ZoomLevel from "./ZoomLevel";
import { useSeatMapContext } from "../context";
import useSeatMapStore from "@stores/useSeatMapStore";
import LayoutInfo from "./LayoutInfo";
import useDimensionsListener from "../hooks/useDimensionsListener";
import { maintainSquare } from "../lib/dimensions";
import ProgressiveSeatRenderer from "./ProgressiveSeatRenderer";
import SmartZoomLoader from "./SmartZoomLoader";
import { InitialLoadingScreen } from "./InitialLoadingScreen";
import { useCallback, useEffect, useMemo } from "react";
import { calculateViewportBounds, throttle, debounce } from "../lib/performance/viewport";
import { ZOOM_PERFORMANCE_CONFIG, INITIAL_RENDER_CONFIG } from "../lib/config";

export default function Canvas() {
  useDimensionsListener();
  const { stageRef, layerRef, dragLayerRef, transformerRef } = useSeatMapContext();
  const {
    stageScale,
    stageDimensions,
    layoutDimensions,
    setLayoutDimensions,
    seatSections,
    viewportBounds,
    setViewportBounds,
    setStageScale,
    setIsZooming,
    setIsOptimizing,
    isOptimizing,
    optimizationProgress
  } = useSeatMapStore();

  // Calculate total seats and check if we need initial loading screen
  const totalSeats = useMemo(() => {
    return Array.from(seatSections.values()).reduce((total, section) => total + section.seats.size, 0);
  }, [seatSections]);

  const shouldShowInitialLoader = useMemo(() => {
    return isOptimizing && totalSeats > INITIAL_RENDER_CONFIG.PROGRESSIVE_LOADING_THRESHOLD;
  }, [isOptimizing, totalSeats]);

  // Update viewport bounds when stage moves or scales
  const updateViewportBounds = useCallback(() => {
    if (stageRef.current) {
      const bounds = calculateViewportBounds(stageRef.current);
      setViewportBounds(bounds);
    }
  }, [stageRef, setViewportBounds]);

  // Throttled viewport update for performance - slower during zoom for better performance
  const throttledViewportUpdate = useCallback(
    throttle(updateViewportBounds, ZOOM_PERFORMANCE_CONFIG.ZOOM_THROTTLE_DELAY),
    [updateViewportBounds]
  );

  // Debounced viewport update for when zoom/pan stops
  const debouncedViewportUpdate = useCallback(
    debounce(updateViewportBounds, ZOOM_PERFORMANCE_CONFIG.ZOOM_DEBOUNCE_DELAY),
    [updateViewportBounds]
  );

  // Update viewport on mount and stage changes
  useEffect(() => {
    updateViewportBounds();
  }, [updateViewportBounds, stageDimensions]);

  // Optimize stage performance
  useEffect(() => {
    const stage = stageRef.current;
    if (stage) {
      // Set adaptive pixel ratio based on scale for better zoom performance
      const adaptivePixelRatio = stageScale < 0.5 ? 1 : Math.min(window.devicePixelRatio, 2);
      Konva.pixelRatio = adaptivePixelRatio;
    }
  }, [stageRef, stageScale]);

  const handleWheel = (e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();

    if (!stageRef.current) return;
    const stage = stageRef.current;
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    // Set zooming and optimizing states for performance optimization and UI feedback
    setIsZooming(true);
    setIsOptimizing(true);

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    // how to scale? Zoom in? Or zoom out?
    let direction = e.evt.deltaY > 0 ? 1 : -1;

    // when we zoom on trackpad, e.evt.ctrlKey is true
    // in that case lets revert direction
    if (e.evt.ctrlKey) {
      direction = -direction;
    }

    const scaleBy = 1.03;
    const newScale = direction > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    if (newScale < 0.05 || newScale >= 8.01) return;

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };

    stage.position(newPos);
    setStageScale(newScale); // Update scale in store for optimized rendering
    throttledViewportUpdate();
    debouncedViewportUpdate(); // Also update when zoom stops

    // Clear states after optimization is complete
    setTimeout(() => {
      setIsZooming(false);
      setIsOptimizing(false);
    }, ZOOM_PERFORMANCE_CONFIG.ZOOM_DEBOUNCE_DELAY);
  };

  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    const clickedId = e?.target.id();
    const nodes = transformerRef.current?.nodes() || [];
    const currentClickedId = nodes.length > 0 ? nodes[0].id() : null;
    const isAlreadySelected = currentClickedId === clickedId;

    const node = e.target;
    console.log(node);
    if (!node || node instanceof Konva.Stage || isAlreadySelected) {
      transformerRef.current?.nodes([]);
      setLayoutDimensions({ ...layoutDimensions, selected: false });
    } else {
      transformerRef.current?.nodes([node]);
      setLayoutDimensions({ ...layoutDimensions, selected: true });
    }
  };

  return (
    <div className="bg-gray-light100 relative flex h-[calc(100vh-72px)] w-full items-center justify-center">
      <ZoomLevel stageScale={stageScale} />
      <LayoutInfo />

      {/* Initial Loading Screen for Large Maps */}
      <InitialLoadingScreen
        isVisible={shouldShowInitialLoader}
        totalSeats={totalSeats}
        loadedSeats={Math.round((optimizationProgress / 100) * totalSeats)}
      />

      {/* Smart Zoom Loading Indicator */}
      <SmartZoomLoader
        totalSeats={totalSeats}
      />

      <Stage
        ref={stageRef}
        className="border-l border-gray-200 transition-opacity duration-150"
        width={stageDimensions.w}
        height={stageDimensions.h}
        onWheel={handleWheel}
        onClick={handleStageClick}
        draggable
      >
        <Layer ref={layerRef} x={(stageDimensions.w - 500) / 2} y={(stageDimensions.h - 500) / 2}>
          {/* <CanvasLayout /> */}
          {/* Render seat sections */}
          {Array.from(seatSections.values()).map((section) => (
            <ProgressiveSeatRenderer
              key={section.id}
              section={section}
              stageScale={stageScale}
              viewportBounds={viewportBounds}
            />
          ))}
          <Transformer keepRatio={true} boundBoxFunc={maintainSquare} ref={transformerRef} />
        </Layer>
        <Layer ref={dragLayerRef} x={(stageDimensions.w - 500) / 2} y={(stageDimensions.h - 500) / 2} />
      </Stage>
    </div>
  );
}
