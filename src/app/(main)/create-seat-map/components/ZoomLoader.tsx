"use client";
import React from "react";
import { cn } from "@lib/utils";

interface ZoomLoaderProps {
  isVisible: boolean;
  progress?: number; // 0-100 for progress indication
  message?: string;
}

export const ZoomLoader: React.FC<ZoomLoaderProps> = ({ 
  isVisible, 
  progress = 0, 
  message = "Optimizing view..." 
}) => {
  if (!isVisible) return null;

  return (
    <div className="pointer-events-none absolute inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/10 backdrop-blur-[1px]" />
      
      {/* Loader Container */}
      <div className="relative flex flex-col items-center gap-3 rounded-lg bg-white/95 px-6 py-4 shadow-lg backdrop-blur-sm border border-gray-200">
        {/* Spinner */}
        <div className="relative">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
          {progress > 0 && (
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-medium text-blue-600">
                {Math.round(progress)}%
              </span>
            </div>
          )}
        </div>
        
        {/* Message */}
        <div className="text-sm font-medium text-gray-700">
          {message}
        </div>
        
        {/* Progress Bar */}
        {progress > 0 && (
          <div className="w-32 h-1 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-blue-600 transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

// Compact version for minimal UI impact
export const CompactZoomLoader: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="pointer-events-none absolute right-4 top-20 z-50">
      <div className="flex items-center gap-2 rounded-full bg-white/90 px-3 py-2 shadow-md backdrop-blur-sm border border-gray-200">
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
        <span className="text-xs font-medium text-gray-600">Optimizing...</span>
      </div>
    </div>
  );
};

// Pulse loader for very quick operations
export const PulseZoomLoader: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="pointer-events-none absolute inset-0 z-40">
      <div className="absolute inset-0 bg-blue-500/5 animate-pulse" />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600" />
      </div>
    </div>
  );
};

// Progress-based loader with seat count
interface SeatCountLoaderProps {
  isVisible: boolean;
  totalSeats: number;
  renderedSeats: number;
}

export const SeatCountLoader: React.FC<SeatCountLoaderProps> = ({ 
  isVisible, 
  totalSeats, 
  renderedSeats 
}) => {
  if (!isVisible || totalSeats === 0) return null;

  const progress = (renderedSeats / totalSeats) * 100;

  return (
    <div className="pointer-events-none absolute bottom-4 left-1/2 -translate-x-1/2 z-50">
      <div className="flex items-center gap-3 rounded-lg bg-white/95 px-4 py-3 shadow-lg backdrop-blur-sm border border-gray-200">
        {/* Progress Circle */}
        <div className="relative h-8 w-8">
          <svg className="h-8 w-8 -rotate-90" viewBox="0 0 32 32">
            <circle
              cx="16"
              cy="16"
              r="14"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-gray-200"
            />
            <circle
              cx="16"
              cy="16"
              r="14"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              strokeDasharray={`${2 * Math.PI * 14}`}
              strokeDashoffset={`${2 * Math.PI * 14 * (1 - progress / 100)}`}
              className="text-blue-600 transition-all duration-300"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-bold text-blue-600">
              {Math.round(progress)}%
            </span>
          </div>
        </div>
        
        {/* Seat Count */}
        <div className="text-sm">
          <div className="font-medium text-gray-700">
            Rendering seats
          </div>
          <div className="text-xs text-gray-500">
            {renderedSeats.toLocaleString()} / {totalSeats.toLocaleString()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZoomLoader;
