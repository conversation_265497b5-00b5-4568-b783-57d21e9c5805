"use client";
import React, { useEffect, useState } from "react";
import useSeatMapStore from "@stores/useSeatMapStore";
import { CompactZoomLoader, ZoomLoader, PulseZoomLoader } from "./ZoomLoader";

interface SmartZoomLoaderProps {
  totalSeats?: number;
}

export const SmartZoomLoader: React.FC<SmartZoomLoaderProps> = ({ totalSeats = 0 }) => {
  const { 
    isZooming, 
    isOptimizing, 
    optimizationProgress, 
    stageScale 
  } = useSeatMapStore();
  
  const [loadingMessage, setLoadingMessage] = useState("Optimizing view...");
  const [showProgress, setShowProgress] = useState(false);

  // Determine loading message and type based on context
  useEffect(() => {
    if (!isOptimizing && !isZooming) return;

    if (stageScale < 0.3) {
      setLoadingMessage("Optimizing for overview...");
      setShowProgress(totalSeats > 10000);
    } else if (stageScale > 2) {
      setLoadingMessage("Enhancing detail view...");
      setShowProgress(totalSeats > 5000);
    } else {
      setLoadingMessage("Adjusting view...");
      setShowProgress(totalSeats > 20000);
    }
  }, [isOptimizing, isZooming, stageScale, totalSeats]);

  // Quick operations - use pulse loader
  if (isZooming && !isOptimizing) {
    return <PulseZoomLoader isVisible={true} />;
  }

  // Medium operations - use compact loader
  if (isOptimizing && totalSeats < 10000) {
    return <CompactZoomLoader isVisible={true} />;
  }

  // Large operations - use full loader with progress
  if (isOptimizing && showProgress) {
    return (
      <ZoomLoader 
        isVisible={true} 
        progress={optimizationProgress}
        message={loadingMessage}
      />
    );
  }

  // Default compact loader
  if (isOptimizing) {
    return <CompactZoomLoader isVisible={true} />;
  }

  return null;
};

export default SmartZoomLoader;
