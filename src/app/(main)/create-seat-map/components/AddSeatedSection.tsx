import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { calcRectangleSeats, createSeatMap, SeatSection } from "../lib/seats";
import { DEFAULT_LABELING_CONFIG } from "../lib/labeling/seatLabeling";
import { INITIAL_RENDER_CONFIG } from "../lib/config";
import { useSeatMapContext } from "../context";
import useLayout from "../hooks/useLayout";
import useSeatMapStore from "@stores/useSeatMapStore";
import { nanoid } from "nanoid";

const FormSchema = z.object({
  name: z.string().min(4, { message: "Name must be at least 4 char." }),
  rows: z
    .number()
    .min(1, { message: "At least 1 row required" })
    .max(1000, { message: "Max. 1000 rows allowed" }),
  seatsPerRow: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(1000, { message: "Max. 1000 seats per row allowed" }),
});

export default function AddSeatedSection() {
  const { stageRef } = useSeatMapContext();
  const { getInsertPosition } = useLayout();
  const { addSeatSection, setNewItemView, setStageScale } = useSeatMapStore();
  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: { name: "Howdy", rows: 20, seatsPerRow: 25 },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  // Calculate total seats for performance warning
  const rows = form.watch("rows");
  const seatsPerRow = form.watch("seatsPerRow");
  const totalSeats = rows * seatsPerRow;
  const isLargeMap = totalSeats > INITIAL_RENDER_CONFIG.PROGRESSIVE_LOADING_THRESHOLD;

  const onSubmit = async () => {
    try {
      const { x, y } = getInsertPosition();
      const name = form.getValues("name");
      const rows = form.getValues("rows");
      const seatsPerRow = form.getValues("seatsPerRow");
      const { width, height, seats } = calcRectangleSeats({ rows, seatsPerRow });

      // Create seat section data structure
      const sectionId = nanoid();
      const seatSection: SeatSection = {
        id: sectionId,
        name,
        x,
        y,
        width,
        height,
        rows,
        seatsPerRow,
        seats: createSeatMap(seats),
        labelingConfig: DEFAULT_LABELING_CONFIG,
        isVisible: true,
      };

      // Add to store
      addSeatSection(seatSection);

      // For large seat maps, set initial zoom to overview level
      const totalSeats = rows * seatsPerRow;
      if (totalSeats > INITIAL_RENDER_CONFIG.PROGRESSIVE_LOADING_THRESHOLD) {
        // Set zoom to overview level for large maps
        const stage = stageRef.current;
        if (stage) {
          const initialScale = INITIAL_RENDER_CONFIG.LARGE_MAP_INITIAL_SCALE;
          stage.scale({ x: initialScale, y: initialScale });

          // Center the view on the new section
          const stageSize = stage.size();
          const centerX = (stageSize.width - width * initialScale) / 2;
          const centerY = (stageSize.height - height * initialScale) / 2;
          stage.position({ x: centerX, y: centerY });

          setStageScale(initialScale);
        }
      }

      // Close the panel
      setNewItemView(null);

      // Reset form
      form.reset();
    } catch (error) {
      console.error("Error creating seated section:", error);
    }
  };

  return (
    <Form {...form}>
      <form className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Section name"
          showIcon={false}
        />
        <CustomNumberInput
          label="Rows"
          key="numberOfRows"
          value={form.watch("rows")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("rows", value)}
        />
        <CustomNumberInput
          label="Seats per row"
          key="seatsPerRow"
          value={form.watch("seatsPerRow")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("seatsPerRow", value)}
        />
        <Disclaimer message={nameError} variant="destructive" />

        {/* Performance warning for large maps */}
        {isLargeMap && (
          <div className="rounded-lg bg-blue-50 border border-blue-200 p-3">
            <div className="flex items-start gap-2">
              <div className="text-blue-600 mt-0.5">ℹ️</div>
              <div className="text-sm">
                <div className="font-medium text-blue-900 mb-1">
                  Large Seat Map ({totalSeats.toLocaleString()} seats)
                </div>
                <div className="text-blue-700">
                  This will use progressive loading for optimal performance.
                  The map will start zoomed out for best viewing.
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-between">
          <Button fullWidth text="Add" onClick={form.handleSubmit(onSubmit)} />
        </div>
      </form>
    </Form>
  );
}
