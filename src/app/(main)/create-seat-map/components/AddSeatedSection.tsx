import Konva from "konva";
import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { useSeatMapContext } from "../context";
import { calcRectangleSeats } from "../lib/seats";
import useLayout from "../hooks/useLayout";
import { SEAT_SIZE } from "../lib/config";

const FormSchema = z.object({
  name: z.string().min(4, { message: "Name must be at least 4 char." }),
  rows: z
    .number()
    .min(1, { message: "At least 1 row required" })
    .max(1000, { message: "Max. 1000 rows allowed" }),
  seatsPerRow: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(1000, { message: "Max. 1000 seats per row allowed" }),
});

export default function AddSeatedSection() {
  const { layerRef, dragLayerRef } = useSeatMapContext();
  const { getInsertPosition } = useLayout();
  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: { name: "Howdy", rows: 20, seatsPerRow: 25 },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  const onSubmit = async () => {
    if (!layerRef.current) return;
    try {
      const { x, y } = getInsertPosition();
      const name = form.getValues("name");
      const rows = form.getValues("rows");
      const seatsPerRow = form.getValues("seatsPerRow");
      const { width, height, seats } = calcRectangleSeats({ rows, seatsPerRow });

      const sectionGroup = new Konva.Group({
        x,
        y,
        draggable: true,
        name,
      });

      const sectionRect = new Konva.Rect({
        x: 0,
        y: 0,
        width,
        height,
        fill: "rgba(200, 200, 200, 0.3)",
        stroke: "black",
        strokeWidth: 2,
        listening: true,
      });
      sectionGroup.add(sectionRect);

      seats.forEach((seatPos) => {
        const seatItem = new Konva.Group({
          id: `seat-item-${seatPos.row}-${seatPos.seatNumber}`,
          name: `seat-item-${seatPos.row}-${seatPos.seatNumber}`,
          listening: false,
          x: seatPos.x,
          y: seatPos.y,
        });

        const seat = new Konva.Rect({
          id: `seat-${seatPos.row}-${seatPos.seatNumber}`,
          perfectDrawEnabled: false,
          listening: false,
          x: 0,
          y: 0,
          width: SEAT_SIZE,
          height: SEAT_SIZE,
          fill: "blue",
        });

        // Add seat label
        const seatLabel = new Konva.Text({
          id: `seat-label-${seatPos.row}-${seatPos.seatNumber}`,
          perfectDrawEnabled: false,
          listening: false,
          x: 0,
          y: 0,
          width: SEAT_SIZE,
          height: SEAT_SIZE,
          text: `${seatPos.row}-${seatPos.seatNumber}`,
          fontSize: Math.min(SEAT_SIZE / 3.5),
          fontFamily: "Arial",
          fill: "white",
          align: "center",
          verticalAlign: "middle",
        });

        seatItem.add(seat);
        seatItem.add(seatLabel);
        sectionGroup.add(seatItem);
      });

      sectionGroup.on("mousedown", (e) => {
        const target = e.target;
        console.log("mousedown on section group", target);
        target.clearCache();
      });

      sectionGroup.on("mouseup", (e) => {
        const target = e.target;
        target.cache({ pixelRatio: 3, imageSmoothingEnabled: false });
      });

      sectionGroup.on("dragstart", (e) => {
        const target = e.target;
        target.moveTo(dragLayerRef.current);
        e.cancelBubble = true;
      });

      sectionGroup.on("dragend", (e) => {
        const target = e.target;
        target.moveTo(layerRef.current);
        e.cancelBubble = true;
      });

      //sectionGroup.cache({ pixelRatio: 3, imageSmoothingEnabled: false });

      layerRef.current.add(sectionGroup);
      layerRef.current.batchDraw();
    } catch (e) {}
  };

  return (
    <Form {...form}>
      <form className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Section name"
          showIcon={false}
        />
        <CustomNumberInput
          label="Rows"
          key="numberOfRows"
          value={form.watch("rows")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("rows", value)}
        />
        <CustomNumberInput
          label="Seats per row"
          key="seatsPerRow"
          value={form.watch("seatsPerRow")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("seatsPerRow", value)}
        />
        <Disclaimer message={nameError} variant="destructive" />
        <div className="flex justify-between">
          <Button fullWidth text="Add" onClick={form.handleSubmit(onSubmit)} />
        </div>
      </form>
    </Form>
  );
}
