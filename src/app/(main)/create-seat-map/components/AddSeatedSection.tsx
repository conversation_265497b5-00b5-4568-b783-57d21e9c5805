import Disclaimer from "@components/primitive/Disclaimer";
import { CustomNumberInput } from "@components/primitive/CustomNumberInput";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Form } from "@components/shadcn/Form";
import Name from "@components/form/Name";
import Button from "@components/primitive/Button";
import { calcRectangleSeats, createSeatMap, SeatSection } from "../lib/seats";
import { DEFAULT_LABELING_CONFIG } from "../lib/labeling/seatLabeling";
import useLayout from "../hooks/useLayout";
import useSeatMapStore from "@stores/useSeatMapStore";
import { nanoid } from "nanoid";

const FormSchema = z.object({
  name: z.string().min(4, { message: "Name must be at least 4 char." }),
  rows: z
    .number()
    .min(1, { message: "At least 1 row required" })
    .max(1000, { message: "Max. 1000 rows allowed" }),
  seatsPerRow: z
    .number()
    .min(1, { message: "At least 1 seat required" })
    .max(1000, { message: "Max. 1000 seats per row allowed" }),
});

export default function AddSeatedSection() {
  const { getInsertPosition } = useLayout();
  const { addSeatSection, setNewItemView } = useSeatMapStore();
  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: { name: "Howdy", rows: 20, seatsPerRow: 25 },
  });

  const formErrors = form.formState.errors;
  const nameError = formErrors.name?.message;

  const onSubmit = async () => {
    try {
      const { x, y } = getInsertPosition();
      const name = form.getValues("name");
      const rows = form.getValues("rows");
      const seatsPerRow = form.getValues("seatsPerRow");
      const { width, height, seats } = calcRectangleSeats({ rows, seatsPerRow });

      // Create seat section data structure
      const sectionId = nanoid();
      const seatSection: SeatSection = {
        id: sectionId,
        name,
        x,
        y,
        width,
        height,
        rows,
        seatsPerRow,
        seats: createSeatMap(seats),
        labelingConfig: DEFAULT_LABELING_CONFIG,
        isVisible: true,
      };

      // Add to store
      addSeatSection(seatSection);

      // Close the panel
      setNewItemView(null);

      // Reset form
      form.reset();
    } catch (error) {
      console.error("Error creating seated section:", error);
    }
  };

  return (
    <Form {...form}>
      <form className="flex w-full flex-col justify-center gap-4">
        <Name
          form={form}
          handleChange={(value) => form.setValue("name", value)}
          placeholder="Section name"
          showIcon={false}
        />
        <CustomNumberInput
          label="Rows"
          key="numberOfRows"
          value={form.watch("rows")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("rows", value)}
        />
        <CustomNumberInput
          label="Seats per row"
          key="seatsPerRow"
          value={form.watch("seatsPerRow")}
          min={1}
          max={1000}
          onChange={(value) => form.setValue("seatsPerRow", value)}
        />
        <Disclaimer message={nameError} variant="destructive" />
        <div className="flex justify-between">
          <Button fullWidth text="Add" onClick={form.handleSubmit(onSubmit)} />
        </div>
      </form>
    </Form>
  );
}
