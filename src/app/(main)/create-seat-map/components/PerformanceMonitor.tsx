"use client";
import React, { useEffect, useState, useRef } from "react";
import useSeatMapStore from "@stores/useSeatMapStore";

interface PerformanceMonitorProps {
  isVisible?: boolean;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ isVisible = false }) => {
  const { isZooming, stageScale, seatSections } = useSeatMapStore();
  const [fps, setFps] = useState(0);
  const [renderTime, setRenderTime] = useState(0);
  const [visibleSeats, setVisibleSeats] = useState(0);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const renderStart = useRef(0);

  // FPS monitoring
  useEffect(() => {
    let animationId: number;
    
    const measureFPS = () => {
      frameCount.current++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime.current >= 1000) {
        setFps(Math.round((frameCount.current * 1000) / (currentTime - lastTime.current)));
        frameCount.current = 0;
        lastTime.current = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };
    
    measureFPS();
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  // Render time monitoring
  useEffect(() => {
    renderStart.current = performance.now();
  });

  useEffect(() => {
    const renderEnd = performance.now();
    setRenderTime(renderEnd - renderStart.current);
  });

  // Calculate visible seats
  useEffect(() => {
    const total = Array.from(seatSections.values()).reduce((sum, section) => sum + section.seats.size, 0);
    setVisibleSeats(total);
  }, [seatSections]);

  if (!isVisible) return null;

  const totalSeats = Array.from(seatSections.values()).reduce((sum, section) => sum + section.seats.size, 0);
  const fpsColor = fps >= 50 ? "text-green-600" : fps >= 30 ? "text-yellow-600" : "text-red-600";
  const renderColor = renderTime <= 16 ? "text-green-600" : renderTime <= 33 ? "text-yellow-600" : "text-red-600";

  return (
    <div className="pointer-events-none absolute bottom-4 left-4 z-50">
      <div className="rounded-lg bg-black/80 p-3 text-xs text-white backdrop-blur-sm">
        <div className="font-bold text-white mb-2">Performance Monitor</div>
        
        <div className="grid grid-cols-2 gap-x-4 gap-y-1">
          <div className="text-gray-300">FPS:</div>
          <div className={fpsColor}>{fps}</div>
          
          <div className="text-gray-300">Render:</div>
          <div className={renderColor}>{renderTime.toFixed(1)}ms</div>
          
          <div className="text-gray-300">Scale:</div>
          <div className="text-blue-400">{Math.round(stageScale * 100)}%</div>
          
          <div className="text-gray-300">Seats:</div>
          <div className="text-purple-400">{totalSeats.toLocaleString()}</div>
          
          <div className="text-gray-300">Zooming:</div>
          <div className={isZooming ? "text-red-400" : "text-green-400"}>
            {isZooming ? "YES" : "NO"}
          </div>
        </div>
        
        <div className="mt-2 pt-2 border-t border-gray-600">
          <div className="text-gray-400 text-xs">
            Target: 60fps, &lt;16ms render
          </div>
        </div>
      </div>
    </div>
  );
};

// Compact version for production
export const CompactPerformanceMonitor: React.FC<{ isVisible?: boolean }> = ({ isVisible = false }) => {
  const { isZooming, stageScale } = useSeatMapStore();
  const [fps, setFps] = useState(0);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());

  useEffect(() => {
    let animationId: number;
    
    const measureFPS = () => {
      frameCount.current++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime.current >= 1000) {
        setFps(Math.round((frameCount.current * 1000) / (currentTime - lastTime.current)));
        frameCount.current = 0;
        lastTime.current = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };
    
    measureFPS();
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  if (!isVisible) return null;

  const fpsColor = fps >= 50 ? "text-green-500" : fps >= 30 ? "text-yellow-500" : "text-red-500";

  return (
    <div className="pointer-events-none absolute bottom-4 left-4 z-50">
      <div className="rounded-md bg-black/70 px-2 py-1 text-xs backdrop-blur-sm">
        <span className="text-gray-300">FPS: </span>
        <span className={fpsColor}>{fps}</span>
        {isZooming && <span className="ml-2 text-red-400">ZOOM</span>}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
