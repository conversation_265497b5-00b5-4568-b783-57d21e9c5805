import React from "react";
import useSeatMapStore from "@stores/useSeatMapStore";

interface Props {
  stageScale: number;
}

export default function ZoomLevel({ stageScale }: Props) {
  const { isOptimizing, optimizationProgress } = useSeatMapStore();

  return (
    <div className="pointer-events-none absolute right-4 top-2 z-10 rounded-sm border border-gray-200 bg-white p-2 text-xs text-dark300 shadow-sm">
      <div className="flex items-center gap-2">
        {isOptimizing && (
          <div className="h-2 w-2 animate-pulse rounded-full bg-blue-500" />
        )}
        <span>Zoom: {Math.round(stageScale * 100)}%</span>
        {isOptimizing && optimizationProgress > 0 && (
          <span className="text-blue-600">({Math.round(optimizationProgress)}%)</span>
        )}
      </div>
    </div>
  );
}
