"use client";
import { SidePanelHeader } from "@components/builder/PanelHeader";
import { useSeatMapContext } from "../context";
import Konva from "konva";
import useSeatMapStore from "@stores/useSeatMapStore";
import { cn } from "@lib/utils";
import { getPanelHeaderName } from "../lib/panelHeader";
import AddSeatedSection from "./AddSeatedSection";

export default function AddItemPanel() {
  const { newItemView, setNewItemView } = useSeatMapStore();
  const { stageRef, layerRef } = useSeatMapContext();

  const close = () => {
    // Logic to close the side toolbar
  };

  const onBackClick = () => {
    // Logic to handle back click
  };

  const onButtonClick = () => {
    // Logic to handle button click
    console.log("Button clicked");
    if (!layerRef.current) return;

    const circle = new Konva.Circle({
      x: 100,
      y: 100,
      radius: 30,
      fill: "red",
      stroke: "black",
      strokeWidth: 2,
      draggable: true,
    });

    circle.on("dragstart", (e) => {
      e.cancelBubble = true; // Prevent event from bubbling to Stage
    });

    circle.setAttr("name", "Section A");

    circle.on("dragend", (e) => {
      e.cancelBubble = true; // Prevent event from bubbling to Stage
    });

    layerRef.current.add(circle);
    layerRef.current.batchDraw();
  };

  const addRectangle = () => {
    if (!layerRef.current) return;

    const rect = new Konva.Rect({
      x: 20,
      y: 20,
      width: 100,
      height: 50,
      fill: "red",
      draggable: true,
    });

    rect.on("dragend", (e) => {
      e.cancelBubble = true;
    });

    layerRef.current.add(rect);
    layerRef.current.batchDraw();
  };

  const getJson = () => {
    if (!layerRef.current) return;
    const json = layerRef.current.toJSON();
    console.log("Stage JSON:", json);
  };

  return (
    <div
      className={cn(
        "absolute left-0 top-0 flex h-full w-[400px] flex-shrink-0 flex-col border-gray-200 bg-white",
        !!newItemView ? "z-10" : "-z-10",
      )}
    >
      <SidePanelHeader
        title={getPanelHeaderName(newItemView)}
        onBackClick={() => setNewItemView(null)}
        backTitle="Back"
      />
      <div className="flex flex-col gap-8 overflow-y-auto px-5 py-4">
        {newItemView === "seated-section" && <AddSeatedSection />}
      </div>
    </div>
  );
}
