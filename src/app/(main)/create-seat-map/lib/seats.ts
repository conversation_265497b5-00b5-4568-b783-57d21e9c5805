import Konva from "konva";
import { SEAT_SIZE, SEAT_GAP, SHAPE_PADDING } from "./config";
import { LabelingConfig, DEFAULT_LABELING_CONFIG, generateFullSeatLabel } from "./labeling/seatLabeling";

export interface SeatPosition {
  x: number;
  y: number;
  row: number;
  seatNumber: number;
  id: string;
  label: string; // Generated label based on labeling config
  isActive: boolean;
  isVisible?: boolean; // For viewport culling
}

export interface SeatSection {
  id: string;
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rows: number;
  seatsPerRow: number;
  seats: Map<string, SeatPosition>; // Use Map for O(1) lookups
  labelingConfig: LabelingConfig;
  isVisible?: boolean;
}

export interface SectionDimensions {
  width: number;
  height: number;
  seats: SeatPosition[];
}

interface CalcRectangleSeats {
  rows: number;
  seatsPerRow: number;
  labelingConfig?: LabelingConfig;
}

export const calcRectangleSeats = ({ rows, seatsPerRow, labelingConfig = DEFAULT_LABELING_CONFIG }: CalcRectangleSeats) => {
  // Calculate the total width needed for seats and gaps
  const seatsWidth = seatsPerRow * SEAT_SIZE + (seatsPerRow - 1) * SEAT_GAP;
  const totalWidth = seatsWidth + SHAPE_PADDING * 2;

  // Calculate the total height needed for seats and gaps
  const seatsHeight = rows * SEAT_SIZE + (rows - 1) * SEAT_GAP;
  const totalHeight = seatsHeight + SHAPE_PADDING * 2;

  const seats: SeatPosition[] = [];

  for (let row = 0; row < rows; row++) {
    for (let seat = 0; seat < seatsPerRow; seat++) {
      const x = SHAPE_PADDING + seat * (SEAT_SIZE + SEAT_GAP);
      const y = SHAPE_PADDING + row * (SEAT_SIZE + SEAT_GAP);
      const label = generateFullSeatLabel(row, seat, seatsPerRow, labelingConfig);
      const seatId = `${row + 1}-${seat + 1}`;
      seats.push({
        x,
        y,
        row: row + 1,
        seatNumber: seat + 1,
        id: seatId,
        label,
        isActive: true, // Default to active
        isVisible: true
      });
    }
  }

  return {
    width: totalWidth,
    height: totalHeight,
    seats,
  };
};

// Utility functions for seat management
export const createSeatMap = (seats: SeatPosition[]): Map<string, SeatPosition> => {
  const seatMap = new Map<string, SeatPosition>();
  seats.forEach(seat => {
    seatMap.set(seat.id, seat);
  });
  return seatMap;
};

export const toggleSeatState = (seatMap: Map<string, SeatPosition>, seatId: string): boolean => {
  const seat = seatMap.get(seatId);
  if (seat) {
    seat.isActive = !seat.isActive;
    return seat.isActive;
  }
  return false;
};

export const getSeatById = (seatMap: Map<string, SeatPosition>, seatId: string): SeatPosition | undefined => {
  return seatMap.get(seatId);
};
