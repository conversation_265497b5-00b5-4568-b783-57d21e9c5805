import Konva from "konva";
import { SeatPosition, SeatSection } from "../seats";
import { SEAT_SIZE } from "../config";

export interface CachedZoomLevel {
  scale: number;
  visibleSeats: Map<string, SeatPosition>;
  renderedLayer: Konva.Layer;
  viewportBounds: { x: number; y: number; width: number; height: number };
}

export interface SeatMapCache {
  sections: Map<string, SeatSection>;
  zoomLevels: Map<number, CachedZoomLevel>;
  seatClickHandlers: Map<string, () => void>;
  isFullyCached: boolean;
  totalSeats: number;
}

export class SeatMapCacheManager {
  private cache: SeatMapCache;
  private stageRef: React.RefObject<Konva.Stage>;
  private onSeatClick: (sectionId: string, seatId: string) => void;

  // Pre-defined zoom levels to cache
  private readonly ZOOM_LEVELS = [0.1, 0.2, 0.3, 0.5, 0.7, 1.0, 1.5, 2.0, 3.0, 4.0, 5.0];

  constructor(
    stageRef: React.RefObject<Konva.Stage>,
    onSeatClick: (sectionId: string, seatId: string) => void
  ) {
    this.stageRef = stageRef;
    this.onSeatClick = onSeatClick;
    this.cache = {
      sections: new Map(),
      zoomLevels: new Map(),
      seatClickHandlers: new Map(),
      isFullyCached: false,
      totalSeats: 0,
    };
  }

  /**
   * Pre-cache everything - seats, zoom levels, interactions
   */
  async preCacheEverything(
    sections: Map<string, SeatSection>,
    onProgress?: (progress: number, message: string) => void
  ): Promise<void> {
    const totalSteps = this.ZOOM_LEVELS.length + sections.size + 1;
    let currentStep = 0;

    onProgress?.(0, "Initializing cache...");

    // Store sections
    this.cache.sections = new Map(sections);
    this.cache.totalSeats = Array.from(sections.values()).reduce(
      (total, section) => total + section.seats.size,
      0
    );

    currentStep++;
    onProgress?.((currentStep / totalSteps) * 100, "Caching seat sections...");

    // Pre-cache all zoom levels
    for (const zoomLevel of this.ZOOM_LEVELS) {
      await this.cacheZoomLevel(zoomLevel);
      currentStep++;
      onProgress?.(
        (currentStep / totalSteps) * 100,
        `Caching zoom level ${Math.round(zoomLevel * 100)}%...`
      );
    }

    // Pre-cache all seat interactions
    await this.cacheSeatInteractions();
    currentStep++;
    onProgress?.((currentStep / totalSteps) * 100, "Caching interactions...");

    this.cache.isFullyCached = true;
    onProgress?.(100, "Cache complete!");
  }

  /**
   * Cache a specific zoom level with all visible seats
   */
  private async cacheZoomLevel(scale: number): Promise<void> {
    if (!this.stageRef.current) return;

    const stage = this.stageRef.current;
    const stageSize = stage.size();

    // Calculate viewport bounds for this zoom level
    const viewportBounds = {
      x: 0,
      y: 0,
      width: stageSize.width / scale,
      height: stageSize.height / scale,
    };

    // Create a new layer for this zoom level
    const layer = new Konva.Layer();

    // Calculate visible seats for this zoom level
    const visibleSeats = new Map<string, SeatPosition>();

    for (const section of this.cache.sections.values()) {
      // Create section group
      const sectionGroup = new Konva.Group({
        x: section.x,
        y: section.y,
        id: `section-${section.id}-zoom-${scale}`,
      });

      // Add section background
      const sectionBg = new Konva.Rect({
        x: 0,
        y: 0,
        width: section.width,
        height: section.height,
        fill: "rgba(200, 200, 200, 0.1)",
        stroke: "#d1d5db",
        strokeWidth: scale > 0.5 ? 1 : 0,
        listening: false,
      });
      sectionGroup.add(sectionBg);

      // Add section label (if zoom level is appropriate)
      if (scale > 0.3) {
        const sectionLabel = new Konva.Text({
          x: 10,
          y: 10,
          text: section.name,
          fontSize: Math.min(14 * scale, 14),
          fontFamily: "Arial",
          fill: "#374151",
          listening: false,
        });
        sectionGroup.add(sectionLabel);
      }

      // Add seats that are visible at this zoom level
      for (const seat of section.seats.values()) {
        if (this.isSeatVisibleAtZoom(seat, section, viewportBounds, scale)) {
          visibleSeats.set(seat.id, seat);

          // Create seat visual
          const seatGroup = this.createSeatVisual(seat, section.id, scale);
          sectionGroup.add(seatGroup);
        }
      }

      layer.add(sectionGroup);
    }

    // Cache this zoom level
    this.cache.zoomLevels.set(scale, {
      scale,
      visibleSeats,
      renderedLayer: layer,
      viewportBounds,
    });
  }

  /**
   * Create visual representation of a seat
   */
  private createSeatVisual(seat: SeatPosition, sectionId: string, scale: number): Konva.Group {
    const seatGroup = new Konva.Group({
      x: seat.x,
      y: seat.y,
      id: `seat-${seat.id}`,
      name: `seat-clickable`,
    });

    const seatColor = seat.isActive ? "#3b82f6" : "#9ca3af";
    const textColor = seat.isActive ? "white" : "#374151";

    // Seat rectangle
    const seatRect = new Konva.Rect({
      width: SEAT_SIZE,
      height: SEAT_SIZE,
      fill: seatColor,
      stroke: seat.isActive ? "#1d4ed8" : "#6b7280",
      strokeWidth: scale > 0.8 ? 1 : 0,
      cornerRadius: scale > 0.5 ? 2 : 0,
      perfectDrawEnabled: false,
    });

    seatGroup.add(seatRect);

    // Seat label (if zoom level is appropriate)
    if (scale > 0.5) {
      const seatText = new Konva.Text({
        width: SEAT_SIZE,
        height: SEAT_SIZE,
        text: seat.label,
        fontSize: Math.min(SEAT_SIZE / 3.5, 8) * Math.min(scale, 1.5),
        fontFamily: "Arial",
        fill: textColor,
        align: "center",
        verticalAlign: "middle",
        perfectDrawEnabled: false,
        listening: false,
      });
      seatGroup.add(seatText);
    }

    // Make seat clickable
    seatGroup.on("click tap", () => {
      this.onSeatClick(sectionId, seat.id);
    });

    return seatGroup;
  }

  /**
   * Check if seat is visible at given zoom level
   */
  private isSeatVisibleAtZoom(
    seat: SeatPosition,
    section: SeatSection,
    viewportBounds: { x: number; y: number; width: number; height: number },
    scale: number
  ): boolean {
    const buffer = Math.max(100, 500 / scale);
    const absoluteX = section.x + seat.x;
    const absoluteY = section.y + seat.y;

    return (
      absoluteX + SEAT_SIZE >= viewportBounds.x - buffer &&
      absoluteX <= viewportBounds.x + viewportBounds.width + buffer &&
      absoluteY + SEAT_SIZE >= viewportBounds.y - buffer &&
      absoluteY <= viewportBounds.y + viewportBounds.height + buffer
    );
  }

  /**
   * Pre-cache all seat click handlers
   */
  private async cacheSeatInteractions(): Promise<void> {
    for (const section of this.cache.sections.values()) {
      for (const seat of section.seats.values()) {
        const handler = () => this.onSeatClick(section.id, seat.id);
        this.cache.seatClickHandlers.set(seat.id, handler);
      }
    }
  }

  /**
   * Get the best cached zoom level for current scale
   */
  getBestZoomLevel(currentScale: number): CachedZoomLevel | null {
    if (!this.cache.isFullyCached) return null;

    // Find the closest cached zoom level
    let bestMatch: number | null = null;
    let smallestDiff = Infinity;

    for (const cachedScale of this.cache.zoomLevels.keys()) {
      const diff = Math.abs(cachedScale - currentScale);
      if (diff < smallestDiff) {
        smallestDiff = diff;
        bestMatch = cachedScale;
      }
    }

    return bestMatch ? this.cache.zoomLevels.get(bestMatch) || null : null;
  }

  /**
   * Update seat state and refresh all cached zoom levels
   */
  async updateSeatState(sectionId: string, seatId: string, isActive: boolean): Promise<void> {
    // Update in cache
    const section = this.cache.sections.get(sectionId);
    if (section) {
      const seat = section.seats.get(seatId);
      if (seat) {
        seat.isActive = isActive;

        // Refresh all zoom levels that contain this seat
        for (const [scale, zoomLevel] of this.cache.zoomLevels.entries()) {
          if (zoomLevel.visibleSeats.has(seatId)) {
            await this.refreshSeatInZoomLevel(scale, sectionId, seat);
          }
        }
      }
    }
  }

  /**
   * Refresh a specific seat in a specific zoom level
   */
  private async refreshSeatInZoomLevel(
    scale: number,
    sectionId: string,
    seat: SeatPosition
  ): Promise<void> {
    const zoomLevel = this.cache.zoomLevels.get(scale);
    if (!zoomLevel) return;

    // Find and update the seat visual in the cached layer
    const seatNode = zoomLevel.renderedLayer.findOne(`#seat-${seat.id}`);
    if (seatNode && seatNode instanceof Konva.Group) {
      // Update seat visual
      const seatRect = seatNode.findOne("Rect");
      if (seatRect) {
        const seatColor = seat.isActive ? "#3b82f6" : "#9ca3af";
        seatRect.fill(seatColor);
        seatRect.stroke(seat.isActive ? "#1d4ed8" : "#6b7280");
      }

      const seatText = seatNode.findOne("Text");
      if (seatText) {
        const textColor = seat.isActive ? "white" : "#374151";
        seatText.fill(textColor);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      isFullyCached: this.cache.isFullyCached,
      totalSeats: this.cache.totalSeats,
      cachedZoomLevels: this.cache.zoomLevels.size,
      cachedSections: this.cache.sections.size,
      cachedInteractions: this.cache.seatClickHandlers.size,
    };
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    // Destroy all cached layers
    for (const zoomLevel of this.cache.zoomLevels.values()) {
      zoomLevel.renderedLayer.destroy();
    }

    this.cache = {
      sections: new Map(),
      zoomLevels: new Map(),
      seatClickHandlers: new Map(),
      isFullyCached: false,
      totalSeats: 0,
    };
  }
}

export default SeatMapCacheManager;
