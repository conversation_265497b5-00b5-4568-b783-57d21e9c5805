import Konva from "konva";

export interface ViewportBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Calculate viewport bounds from stage position and scale
 */
export const calculateViewportBounds = (
  stage: Konva.Stage,
  buffer: number = 200
): ViewportBounds => {
  const stagePos = stage.position();
  const stageScale = stage.scaleX();
  const stageSize = stage.size();

  return {
    x: (-stagePos.x / stageScale) - buffer,
    y: (-stagePos.y / stageScale) - buffer,
    width: (stageSize.width / stageScale) + (buffer * 2),
    height: (stageSize.height / stageScale) + (buffer * 2),
  };
};

/**
 * Check if a rectangle intersects with viewport bounds
 */
export const isRectInViewport = (
  rectX: number,
  rectY: number,
  rectWidth: number,
  rectHeight: number,
  viewport: ViewportBounds
): boolean => {
  return !(
    rectX + rectWidth < viewport.x ||
    rectX > viewport.x + viewport.width ||
    rectY + rectHeight < viewport.y ||
    rectY > viewport.y + viewport.height
  );
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  
  return (...args: Parameters<T>) => {
    const currentTime = Date.now();
    
    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Performance monitoring utility
 */
export class PerformanceMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private fps = 0;
  
  public measureFrame(): number {
    this.frameCount++;
    const currentTime = performance.now();
    
    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.frameCount = 0;
      this.lastTime = currentTime;
    }
    
    return this.fps;
  }
  
  public getFPS(): number {
    return this.fps;
  }
}

/**
 * Object pool for reusing Konva objects
 */
export class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize: number = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }
  
  public acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }
  
  public release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
  
  public getPoolSize(): number {
    return this.pool.length;
  }
}
