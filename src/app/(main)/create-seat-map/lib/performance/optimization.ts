import Konva from "konva";

/**
 * Performance configuration for different seat map sizes
 */
export const PERFORMANCE_CONFIG = {
  SMALL: {
    maxSeats: 1000,
    enableVirtualization: false,
    cacheThreshold: 100,
    batchSize: 50,
  },
  MEDIUM: {
    maxSeats: 10000,
    enableVirtualization: true,
    cacheThreshold: 500,
    batchSize: 100,
  },
  LARGE: {
    maxSeats: 100000,
    enableVirtualization: true,
    cacheThreshold: 1000,
    batchSize: 200,
  },
  XLARGE: {
    maxSeats: Infinity,
    enableVirtualization: true,
    cacheThreshold: 2000,
    batchSize: 500,
  },
};

/**
 * Get performance configuration based on total seat count
 */
export const getPerformanceConfig = (totalSeats: number) => {
  if (totalSeats <= PERFORMANCE_CONFIG.SMALL.maxSeats) {
    return PERFORMANCE_CONFIG.SMALL;
  } else if (totalSeats <= PERFORMANCE_CONFIG.MEDIUM.maxSeats) {
    return PERFORMANCE_CONFIG.MEDIUM;
  } else if (totalSeats <= PERFORMANCE_CONFIG.LARGE.maxSeats) {
    return PERFORMANCE_CONFIG.LARGE;
  } else {
    return PERFORMANCE_CONFIG.XLARGE;
  }
};

/**
 * Batch processing utility for large operations
 */
export class BatchProcessor<T> {
  private queue: T[] = [];
  private processing = false;
  private batchSize: number;
  private processFn: (batch: T[]) => Promise<void> | void;

  constructor(batchSize: number, processFn: (batch: T[]) => Promise<void> | void) {
    this.batchSize = batchSize;
    this.processFn = processFn;
  }

  public add(item: T): void {
    this.queue.push(item);
    this.processQueue();
  }

  public addBatch(items: T[]): void {
    this.queue.push(...items);
    this.processQueue();
  }

  private async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.batchSize);
      await this.processFn(batch);
      
      // Yield control to prevent blocking the main thread
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    this.processing = false;
  }

  public clear(): void {
    this.queue = [];
  }

  public getQueueSize(): number {
    return this.queue.length;
  }
}

/**
 * Memory-efficient seat data structure using bit arrays
 */
export class SeatStateManager {
  private activeStates: Uint8Array;
  private visibleStates: Uint8Array;
  private totalSeats: number;

  constructor(totalSeats: number) {
    this.totalSeats = totalSeats;
    // Use bit arrays for memory efficiency
    const byteLength = Math.ceil(totalSeats / 8);
    this.activeStates = new Uint8Array(byteLength);
    this.visibleStates = new Uint8Array(byteLength);
    
    // Initialize all seats as active and visible
    this.activeStates.fill(0xFF);
    this.visibleStates.fill(0xFF);
  }

  public isActive(seatIndex: number): boolean {
    const byteIndex = Math.floor(seatIndex / 8);
    const bitIndex = seatIndex % 8;
    return (this.activeStates[byteIndex] & (1 << bitIndex)) !== 0;
  }

  public setActive(seatIndex: number, active: boolean): void {
    const byteIndex = Math.floor(seatIndex / 8);
    const bitIndex = seatIndex % 8;
    
    if (active) {
      this.activeStates[byteIndex] |= (1 << bitIndex);
    } else {
      this.activeStates[byteIndex] &= ~(1 << bitIndex);
    }
  }

  public isVisible(seatIndex: number): boolean {
    const byteIndex = Math.floor(seatIndex / 8);
    const bitIndex = seatIndex % 8;
    return (this.visibleStates[byteIndex] & (1 << bitIndex)) !== 0;
  }

  public setVisible(seatIndex: number, visible: boolean): void {
    const byteIndex = Math.floor(seatIndex / 8);
    const bitIndex = seatIndex % 8;
    
    if (visible) {
      this.visibleStates[byteIndex] |= (1 << bitIndex);
    } else {
      this.visibleStates[byteIndex] &= ~(1 << bitIndex);
    }
  }

  public toggleActive(seatIndex: number): boolean {
    const isCurrentlyActive = this.isActive(seatIndex);
    this.setActive(seatIndex, !isCurrentlyActive);
    return !isCurrentlyActive;
  }

  public getActiveCount(): number {
    let count = 0;
    for (let i = 0; i < this.totalSeats; i++) {
      if (this.isActive(i)) count++;
    }
    return count;
  }

  public getVisibleCount(): number {
    let count = 0;
    for (let i = 0; i < this.totalSeats; i++) {
      if (this.isVisible(i)) count++;
    }
    return count;
  }
}

/**
 * Konva performance optimizations
 */
export const optimizeKonvaPerformance = (stage: Konva.Stage): void => {
  // Disable hit detection for better performance
  stage.listening(false);
  
  // Use faster rendering mode
  Konva.pixelRatio = Math.min(window.devicePixelRatio, 2);
  
  // Optimize stage settings
  stage.perfectDrawEnabled(false);
  stage.shadowForStrokeEnabled(false);
};

/**
 * Adaptive quality based on zoom level
 */
export const getAdaptiveQuality = (scale: number): {
  pixelRatio: number;
  imageSmoothingEnabled: boolean;
  perfectDrawEnabled: boolean;
} => {
  if (scale < 0.5) {
    // Very zoomed out - lowest quality
    return {
      pixelRatio: 0.5,
      imageSmoothingEnabled: false,
      perfectDrawEnabled: false,
    };
  } else if (scale < 1) {
    // Zoomed out - medium quality
    return {
      pixelRatio: 1,
      imageSmoothingEnabled: false,
      perfectDrawEnabled: false,
    };
  } else if (scale < 2) {
    // Normal zoom - good quality
    return {
      pixelRatio: Math.min(window.devicePixelRatio, 2),
      imageSmoothingEnabled: true,
      perfectDrawEnabled: false,
    };
  } else {
    // Zoomed in - highest quality
    return {
      pixelRatio: Math.min(window.devicePixelRatio, 3),
      imageSmoothingEnabled: true,
      perfectDrawEnabled: true,
    };
  }
};

/**
 * Request animation frame wrapper for smooth animations
 */
export const requestAnimationFrameWrapper = (callback: () => void): number => {
  return requestAnimationFrame(callback);
};

/**
 * Cancel animation frame wrapper
 */
export const cancelAnimationFrameWrapper = (id: number): void => {
  cancelAnimationFrame(id);
};
