export const LAYOUT_HEIGHT = 500;
export const LAYOUT_WIDTH = 500;
export const SEAT_SIZE = 20;
export const SEAT_GAP = 8;
export const SHAPE_PADDING = 10;

// Performance configuration for zoom operations
export const ZOOM_PERFORMANCE_CONFIG = {
  // Minimum scale before aggressive optimizations kick in
  AGGRESSIVE_OPTIMIZATION_THRESHOLD: 0.3,

  // Scale threshold for hiding text labels
  TEXT_VISIBILITY_THRESHOLD: 0.5,

  // Scale threshold for hiding section details
  SECTION_DETAILS_THRESHOLD: 0.2,

  // Throttle delay for zoom operations (ms)
  ZOOM_THROTTLE_DELAY: 100,

  // Debounce delay for zoom end detection (ms)
  ZOOM_DEBOUNCE_DELAY: 150,

  // Maximum seats to render during zoom operations
  MAX_SEATS_DURING_ZOOM: 1000,

  // Skip factor for seat rendering when zoomed out
  ZOOM_OUT_SKIP_FACTOR: 4,
};
