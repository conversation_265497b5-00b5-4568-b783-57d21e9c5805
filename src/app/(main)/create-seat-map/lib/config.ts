export const LAYOUT_HEIGHT = 500;
export const LAYOUT_WIDTH = 500;
export const SEAT_SIZE = 20;
export const SEAT_GAP = 8;
export const SHAPE_PADDING = 10;

// Performance configuration for zoom operations
export const ZOOM_PERFORMANCE_CONFIG = {
  // Freeze rendering during zoom - only update when zoom stops
  FREEZE_DURING_ZOOM: true,

  // How long to wait after zoom stops before updating (ms)
  ZOOM_STOP_DELAY: 150,

  // Minimum time between renders during zoom (ms) - very slow to avoid lag
  ZOOM_RENDER_THROTTLE: 500,

  // Scale threshold for hiding text labels
  TEXT_VISIBILITY_THRESHOLD: 0.5,

  // Scale threshold for hiding section details
  SECTION_DETAILS_THRESHOLD: 0.2,
};

// Initial rendering configuration for large seat maps
export const INITIAL_RENDER_CONFIG = {
  // Maximum seats to render on initial load
  MAX_INITIAL_SEATS: 2000,

  // Threshold for enabling progressive loading
  PROGRESSIVE_LOADING_THRESHOLD: 10000,

  // Initial zoom level for large seat maps
  LARGE_MAP_INITIAL_SCALE: 0.1,

  // Batch size for progressive loading
  PROGRESSIVE_BATCH_SIZE: 500,

  // Delay between progressive batches (ms)
  PROGRESSIVE_BATCH_DELAY: 50,
};
