/**
 * Seat labeling and numbering system
 */

export type LabelingScheme = 
  | "numeric" // 1, 2, 3, 4...
  | "alphabetic" // A, B, C, D...
  | "alphanumeric" // A1, A2, B1, B2...
  | "custom"; // Custom pattern

export type NumberingDirection = 
  | "left-to-right"
  | "right-to-left"
  | "center-out"
  | "alternating";

export type RowLabelingScheme = 
  | "numeric" // 1, 2, 3, 4...
  | "alphabetic" // A, B, C, D...
  | "roman" // I, II, III, IV...
  | "custom"; // Custom pattern

export interface LabelingConfig {
  rowScheme: RowLabelingScheme;
  seatScheme: LabelingScheme;
  numberingDirection: NumberingDirection;
  startRow: number | string;
  startSeat: number | string;
  customRowPattern?: string[];
  customSeatPattern?: string[];
  showRowLabels: boolean;
  showSeatLabels: boolean;
  separator: string; // e.g., "-", ":", " "
}

export const DEFAULT_LABELING_CONFIG: LabelingConfig = {
  rowScheme: "numeric",
  seatScheme: "numeric",
  numberingDirection: "left-to-right",
  startRow: 1,
  startSeat: 1,
  showRowLabels: true,
  showSeatLabels: true,
  separator: "-",
};

/**
 * Generate alphabetic labels (A, B, C, ..., Z, AA, AB, ...)
 */
export const generateAlphabeticLabel = (index: number): string => {
  let result = "";
  let num = index;
  
  do {
    result = String.fromCharCode(65 + (num % 26)) + result;
    num = Math.floor(num / 26) - 1;
  } while (num >= 0);
  
  return result;
};

/**
 * Generate roman numerals
 */
export const generateRomanNumeral = (num: number): string => {
  const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
  const symbols = ["M", "CM", "D", "CD", "C", "XC", "L", "XL", "X", "IX", "V", "IV", "I"];
  
  let result = "";
  for (let i = 0; i < values.length; i++) {
    while (num >= values[i]) {
      result += symbols[i];
      num -= values[i];
    }
  }
  return result;
};

/**
 * Generate row label based on scheme
 */
export const generateRowLabel = (
  rowIndex: number, 
  config: LabelingConfig
): string => {
  switch (config.rowScheme) {
    case "numeric":
      return String(typeof config.startRow === "number" ? config.startRow + rowIndex : rowIndex + 1);
    case "alphabetic":
      return generateAlphabeticLabel(rowIndex);
    case "roman":
      return generateRomanNumeral(rowIndex + 1);
    case "custom":
      return config.customRowPattern?.[rowIndex] || String(rowIndex + 1);
    default:
      return String(rowIndex + 1);
  }
};

/**
 * Generate seat label based on scheme and direction
 */
export const generateSeatLabel = (
  seatIndex: number,
  totalSeats: number,
  config: LabelingConfig
): string => {
  let adjustedIndex = seatIndex;
  
  // Adjust index based on numbering direction
  switch (config.numberingDirection) {
    case "right-to-left":
      adjustedIndex = totalSeats - 1 - seatIndex;
      break;
    case "center-out":
      const center = Math.floor(totalSeats / 2);
      if (seatIndex < center) {
        adjustedIndex = center - 1 - seatIndex;
      } else {
        adjustedIndex = seatIndex - center;
      }
      break;
    case "alternating":
      adjustedIndex = seatIndex % 2 === 0 ? seatIndex / 2 : totalSeats - Math.ceil(seatIndex / 2);
      break;
    default: // left-to-right
      adjustedIndex = seatIndex;
  }
  
  switch (config.seatScheme) {
    case "numeric":
      return String(typeof config.startSeat === "number" ? config.startSeat + adjustedIndex : adjustedIndex + 1);
    case "alphabetic":
      return generateAlphabeticLabel(adjustedIndex);
    case "alphanumeric":
      return `${generateAlphabeticLabel(Math.floor(adjustedIndex / 26))}${(adjustedIndex % 26) + 1}`;
    case "custom":
      return config.customSeatPattern?.[adjustedIndex] || String(adjustedIndex + 1);
    default:
      return String(adjustedIndex + 1);
  }
};

/**
 * Generate full seat label (row + seat)
 */
export const generateFullSeatLabel = (
  rowIndex: number,
  seatIndex: number,
  totalSeats: number,
  config: LabelingConfig
): string => {
  const rowLabel = config.showRowLabels ? generateRowLabel(rowIndex, config) : "";
  const seatLabel = config.showSeatLabels ? generateSeatLabel(seatIndex, totalSeats, config) : "";
  
  if (rowLabel && seatLabel) {
    return `${rowLabel}${config.separator}${seatLabel}`;
  } else if (rowLabel) {
    return rowLabel;
  } else if (seatLabel) {
    return seatLabel;
  } else {
    return `${rowIndex + 1}${config.separator}${seatIndex + 1}`;
  }
};

/**
 * Validate labeling configuration
 */
export const validateLabelingConfig = (config: LabelingConfig): string[] => {
  const errors: string[] = [];
  
  if (config.rowScheme === "custom" && (!config.customRowPattern || config.customRowPattern.length === 0)) {
    errors.push("Custom row pattern is required when using custom row scheme");
  }
  
  if (config.seatScheme === "custom" && (!config.customSeatPattern || config.customSeatPattern.length === 0)) {
    errors.push("Custom seat pattern is required when using custom seat scheme");
  }
  
  if (!config.showRowLabels && !config.showSeatLabels) {
    errors.push("At least one of row labels or seat labels must be enabled");
  }
  
  return errors;
};

/**
 * Generate preview labels for configuration
 */
export const generatePreviewLabels = (
  config: LabelingConfig,
  rows: number = 3,
  seatsPerRow: number = 5
): string[][] => {
  const preview: string[][] = [];
  
  for (let row = 0; row < rows; row++) {
    const rowLabels: string[] = [];
    for (let seat = 0; seat < seatsPerRow; seat++) {
      rowLabels.push(generateFullSeatLabel(row, seat, seatsPerRow, config));
    }
    preview.push(rowLabels);
  }
  
  return preview;
};
