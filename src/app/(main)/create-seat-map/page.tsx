import MainPanel from "./components/MainPanel";
import PreCachedCanvas from "./components/PreCachedCanvas";
import ContextProvider from "./components/ContextProvider";
import AddItemPanel from "./components/AddItemPanel";

// commented out the below to hide in prod
// uncomment the below in dev
// to see what we've done so far

export default function page() {
  return (
    <div className="relative flex h-[calc(100vh-72px)]">
      <ContextProvider>
        <div className="h-full w-[400px] flex-shrink-0">
          <MainPanel />
          <AddItemPanel />
        </div>
        <Canvas />
      </ContextProvider>
    </div>
  );
}
