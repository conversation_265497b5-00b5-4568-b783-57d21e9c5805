import { create } from "zustand";
import { LAYOUT_HEIGHT, LAYOUT_WIDTH } from "../app/(main)/create-seat-map/lib/config";
import { SeatSection, toggleSeatState } from "../app/(main)/create-seat-map/lib/seats";

export type NewItem =
  | "standing-section"
  | "seated-section"
  | "landmark"
  | "round-table"
  | "rectangle-table"
  | null;

type Dimensions = { w: number; h: number };
export type Position = { x: number; y: number };
type LayoutDimensions = {
  w: number;
  h: number;
  selected: boolean;
};

type ViewportBounds = {
  x: number;
  y: number;
  width: number;
  height: number;
};

interface SeatMapStore {
  newItemView: "standing-section" | "seated-section" | "landmark" | "round-table" | "rectangle-table" | null;
  setNewItemView: (newItemView: NewItem) => void;
  editingItemId: string | null;
  setEditingItemId: (id: string | null) => void;
  stageDimensions: Dimensions;
  setStageDimensions: (dimensions: Dimensions) => void;
  layoutDimensions: LayoutDimensions;
  setLayoutDimensions: (dimensions: LayoutDimensions) => void;
  stageScale: number;
  setStageScale: (scale: number) => void;
  // Seat sections management
  seatSections: Map<string, SeatSection>;
  addSeatSection: (section: SeatSection) => void;
  removeSeatSection: (sectionId: string) => void;
  updateSeatSection: (sectionId: string, updates: Partial<SeatSection>) => void;
  // Seat state management
  toggleSeat: (sectionId: string, seatId: string) => void;
  // Viewport management for performance
  viewportBounds: ViewportBounds;
  setViewportBounds: (bounds: ViewportBounds) => void;
  // Performance settings
  enableVirtualization: boolean;
  setEnableVirtualization: (enabled: boolean) => void;
  // Zoom state for performance optimization
  isZooming: boolean;
  setIsZooming: (zooming: boolean) => void;
  // Loading states
  isOptimizing: boolean;
  setIsOptimizing: (optimizing: boolean) => void;
  optimizationProgress: number;
  setOptimizationProgress: (progress: number) => void;
}

const useSeatMapStore = create<SeatMapStore>((set, get) => ({
  newItemView: null,
  setNewItemView: (newItemView: NewItem) => set({ newItemView }),
  editingItemId: null,
  setEditingItemId: (editingItemId: string | null) => set({ editingItemId }),
  stageDimensions: { w: 0, h: 0 },
  setStageDimensions: (stageDimensions: Dimensions) => set({ stageDimensions }),
  layoutDimensions: { w: LAYOUT_WIDTH, h: LAYOUT_HEIGHT, selected: false },
  setLayoutDimensions: (layoutDimensions: LayoutDimensions) => set({ layoutDimensions }),
  stageScale: 1,
  setStageScale: (stageScale: number) => set({ stageScale }),
  // Seat sections management
  seatSections: new Map<string, SeatSection>(),
  addSeatSection: (section: SeatSection) => {
    const { seatSections } = get();
    const newSections = new Map(seatSections);
    newSections.set(section.id, section);
    set({ seatSections: newSections });
  },
  removeSeatSection: (sectionId: string) => {
    const { seatSections } = get();
    const newSections = new Map(seatSections);
    newSections.delete(sectionId);
    set({ seatSections: newSections });
  },
  updateSeatSection: (sectionId: string, updates: Partial<SeatSection>) => {
    const { seatSections } = get();
    const section = seatSections.get(sectionId);
    if (section) {
      const newSections = new Map(seatSections);
      newSections.set(sectionId, { ...section, ...updates });
      set({ seatSections: newSections });
    }
  },
  // Seat state management
  toggleSeat: (sectionId: string, seatId: string) => {
    const { seatSections } = get();
    const section = seatSections.get(sectionId);
    if (section) {
      toggleSeatState(section.seats, seatId);
      // Trigger re-render by updating the sections map
      const newSections = new Map(seatSections);
      set({ seatSections: newSections });
    }
  },
  // Viewport management for performance
  viewportBounds: { x: 0, y: 0, width: 0, height: 0 },
  setViewportBounds: (bounds: ViewportBounds) => set({ viewportBounds: bounds }),
  // Performance settings
  enableVirtualization: true,
  setEnableVirtualization: (enabled: boolean) => set({ enableVirtualization: enabled }),
  // Zoom state for performance optimization
  isZooming: false,
  setIsZooming: (isZooming: boolean) => set({ isZooming }),
  // Loading states
  isOptimizing: false,
  setIsOptimizing: (isOptimizing: boolean) => set({ isOptimizing }),
  optimizationProgress: 0,
  setOptimizationProgress: (optimizationProgress: number) => set({ optimizationProgress }),
}));

export default useSeatMapStore;
